/**
 * 用户系统测试套件
 * 验证易经"变化适应"智慧的实现效果
 */

import {
  UserSystemFactory,
  UserSystem,
  UserService,
  UserState,
  UserRole,
  AuthMethod,
  Permission,
  CreateUserRequest,
  UserSystemUtils,
} from '../index';

describe('用户系统测试 - 易经变化适应智慧', () => {
  let userSystem: UserSystem;
  let userService: UserService;

  beforeEach(() => {
    // 创建开发环境用户系统
    userSystem = UserSystemFactory.createDevelopmentSystem('test-secret');
    userService = userSystem.users;
  });

  describe('用户创建 - 体现"自强不息"', () => {
    test('应该能够创建基础用户', async () => {
      const request: CreateUserRequest = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        username: 'testuser',
        displayName: 'Test User',
        authMethod: AuthMethod.EMAIL_PASSWORD,
      };

      const user = await userService.createUser(request);

      expect(user.profile.email).toBe(request.email);
      expect(user.profile.username).toBe(request.username);
      expect(user.profile.displayName).toBe(request.displayName);
      expect(user.state).toBe(UserState.PENDING); // 邮箱密码需要验证
      expect(user.permissions.roles).toContain(UserRole.USER);
      expect(user.auth).toHaveLength(1);
      expect(user.auth[0].method).toBe(AuthMethod.EMAIL_PASSWORD);
      expect(user.stateHistory).toHaveLength(1);
    });

    test('应该能够创建OAuth用户', async () => {
      const request: CreateUserRequest = {
        email: '<EMAIL>',
        username: 'oauthuser',
        authMethod: AuthMethod.OAUTH_GOOGLE,
        initialRole: UserRole.USER,
      };

      const user = await userService.createUser(request);

      expect(user.state).toBe(UserState.ACTIVE); // OAuth用户直接激活
      expect(user.auth[0].method).toBe(AuthMethod.OAUTH_GOOGLE);
      expect(user.auth[0].isVerified).toBe(true);
    });

    test('应该拒绝重复邮箱', async () => {
      const request: CreateUserRequest = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        authMethod: AuthMethod.EMAIL_PASSWORD,
      };

      await userService.createUser(request);

      await expect(userService.createUser(request)).rejects.toThrow('Email already exists');
    });

    test('应该拒绝重复用户名', async () => {
      const request1: CreateUserRequest = {
        email: '<EMAIL>',
        username: 'sameusername',
        password: 'SecurePass123!',
        authMethod: AuthMethod.EMAIL_PASSWORD,
      };

      const request2: CreateUserRequest = {
        email: '<EMAIL>',
        username: 'sameusername',
        password: 'SecurePass123!',
        authMethod: AuthMethod.EMAIL_PASSWORD,
      };

      await userService.createUser(request1);

      await expect(userService.createUser(request2)).rejects.toThrow('Username already exists');
    });
  });

  describe('用户认证 - 体现"适应性"', () => {
    let testUser: any;

    beforeEach(async () => {
      const request: CreateUserRequest = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        username: 'authuser',
        authMethod: AuthMethod.EMAIL_PASSWORD,
      };

      testUser = await userService.createUser(request);
      
      // 激活用户以便测试认证
      await userService.changeUserState(testUser.profile.id, UserState.ACTIVE);
    });

    test('应该能够使用邮箱密码认证', async () => {
      const result = await userService.authenticate(
        '<EMAIL>',
        'SecurePass123!',
        AuthMethod.EMAIL_PASSWORD
      );

      expect(result.success).toBe(true);
      expect(result.user).toBeDefined();
      expect(result.token).toBeDefined();
      expect(result.expiresAt).toBeDefined();
    });

    test('应该拒绝错误密码', async () => {
      const result = await userService.authenticate(
        '<EMAIL>',
        'WrongPassword',
        AuthMethod.EMAIL_PASSWORD
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    test('应该拒绝不存在的用户', async () => {
      const result = await userService.authenticate(
        '<EMAIL>',
        'AnyPassword',
        AuthMethod.EMAIL_PASSWORD
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('User not found');
    });
  });

  describe('用户状态管理 - 体现"穷则变，变则通"', () => {
    let testUser: any;
    let adminUser: any;

    beforeEach(async () => {
      // 创建普通用户
      const userRequest: CreateUserRequest = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        authMethod: AuthMethod.EMAIL_PASSWORD,
      };
      testUser = await userService.createUser(userRequest);

      // 创建管理员用户
      const adminRequest: CreateUserRequest = {
        email: '<EMAIL>',
        password: 'AdminPass123!',
        authMethod: AuthMethod.EMAIL_PASSWORD,
        initialRole: UserRole.ADMIN,
      };
      adminUser = await userService.createUser(adminRequest);
      await userService.changeUserState(adminUser.profile.id, UserState.ACTIVE);
    });

    test('应该能够激活待审核用户', async () => {
      expect(testUser.state).toBe(UserState.PENDING);

      const updatedUser = await userService.changeUserState(
        testUser.profile.id,
        UserState.ACTIVE,
        'Manual activation',
        adminUser
      );

      expect(updatedUser.state).toBe(UserState.ACTIVE);
      expect(updatedUser.stateHistory).toHaveLength(2);
      expect(updatedUser.stateHistory[1].fromState).toBe(UserState.PENDING);
      expect(updatedUser.stateHistory[1].toState).toBe(UserState.ACTIVE);
      expect(updatedUser.stateHistory[1].reason).toBe('Manual activation');
    });

    test('应该能够暂停活跃用户', async () => {
      // 先激活用户
      await userService.changeUserState(testUser.profile.id, UserState.ACTIVE);
      
      const updatedUser = await userService.changeUserState(
        testUser.profile.id,
        UserState.SUSPENDED,
        'Policy violation',
        adminUser
      );

      expect(updatedUser.state).toBe(UserState.SUSPENDED);
      expect(updatedUser.metadata?.suspendedAt).toBeDefined();
    });

    test('应该拒绝无权限的状态转换', async () => {
      // 普通用户尝试暂停其他用户
      await expect(
        userService.changeUserState(
          testUser.profile.id,
          UserState.SUSPENDED,
          'Unauthorized attempt',
          testUser
        )
      ).rejects.toThrow('State transition not allowed');
    });

    test('应该能够归档用户', async () => {
      const updatedUser = await userService.changeUserState(
        testUser.profile.id,
        UserState.ARCHIVED,
        'Account cleanup',
        adminUser
      );

      expect(updatedUser.state).toBe(UserState.ARCHIVED);
      expect(updatedUser.metadata?.archivedAt).toBeDefined();
    });
  });

  describe('权限管理 - 体现"平衡性"', () => {
    let testUser: any;
    let adminUser: any;

    beforeEach(async () => {
      const userRequest: CreateUserRequest = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        authMethod: AuthMethod.EMAIL_PASSWORD,
      };
      testUser = await userService.createUser(userRequest);

      const adminRequest: CreateUserRequest = {
        email: '<EMAIL>',
        password: 'AdminPass123!',
        authMethod: AuthMethod.EMAIL_PASSWORD,
        initialRole: UserRole.ADMIN,
      };
      adminUser = await userService.createUser(adminRequest);
    });

    test('应该能够授予权限', async () => {
      await userService.grantPermission(
        testUser.profile.id,
        Permission.MODERATE,
        adminUser
      );

      const updatedUser = await userService.getUserById(testUser.profile.id);
      expect(updatedUser?.permissions.permissions).toContain(Permission.MODERATE);
    });

    test('应该能够撤销权限', async () => {
      // 先授予权限
      await userService.grantPermission(
        testUser.profile.id,
        Permission.MODERATE,
        adminUser
      );

      // 再撤销权限
      await userService.revokePermission(
        testUser.profile.id,
        Permission.MODERATE,
        adminUser
      );

      const updatedUser = await userService.getUserById(testUser.profile.id);
      expect(updatedUser?.permissions.permissions).not.toContain(Permission.MODERATE);
    });

    test('应该拒绝无权限的权限操作', async () => {
      await expect(
        userService.grantPermission(
          testUser.profile.id,
          Permission.ADMIN,
          testUser // 普通用户尝试授予管理员权限
        )
      ).rejects.toThrow('Permission denied');
    });
  });

  describe('用户查询 - 体现"通则久"', () => {
    beforeEach(async () => {
      // 创建多个测试用户
      const users = [
        {
          email: '<EMAIL>',
          username: 'user1',
          authMethod: AuthMethod.EMAIL_PASSWORD,
          initialRole: UserRole.USER,
        },
        {
          email: '<EMAIL>',
          username: 'user2',
          authMethod: AuthMethod.OAUTH_GOOGLE,
          initialRole: UserRole.MODERATOR,
        },
        {
          email: '<EMAIL>',
          username: 'user3',
          authMethod: AuthMethod.EMAIL_PASSWORD,
          initialRole: UserRole.ADMIN,
        },
      ];

      for (const userData of users) {
        await userService.createUser(userData as CreateUserRequest);
      }
    });

    test('应该能够查询所有用户', async () => {
      const result = await userService.queryUsers();

      expect(result.users.length).toBe(3);
      expect(result.total).toBe(3);
    });

    test('应该能够按角色过滤用户', async () => {
      const result = await userService.queryUsers({
        roles: [UserRole.MODERATOR],
      });

      expect(result.users.length).toBe(1);
      expect(result.users[0].permissions.roles).toContain(UserRole.MODERATOR);
    });

    test('应该能够按状态过滤用户', async () => {
      const result = await userService.queryUsers({
        state: [UserState.ACTIVE],
      });

      // OAuth用户默认为ACTIVE状态
      expect(result.users.length).toBe(1);
      expect(result.users[0].state).toBe(UserState.ACTIVE);
    });

    test('应该支持分页查询', async () => {
      const result = await userService.queryUsers({}, {
        limit: 2,
        offset: 0,
      });

      expect(result.users.length).toBe(2);
      expect(result.total).toBe(3);
    });
  });

  describe('系统工具函数', () => {
    test('应该能够验证密码强度', () => {
      const weakPassword = UserSystemUtils.validatePassword('123');
      expect(weakPassword.isValid).toBe(false);
      expect(weakPassword.errors.length).toBeGreaterThan(0);

      const strongPassword = UserSystemUtils.validatePassword('SecurePass123!');
      expect(strongPassword.isValid).toBe(true);
      expect(strongPassword.errors.length).toBe(0);
    });

    test('应该能够生成安全密码', () => {
      const password = UserSystemUtils.generateSecurePassword(12);
      expect(password.length).toBe(12);
      
      const validation = UserSystemUtils.validatePassword(password);
      expect(validation.isValid).toBe(true);
    });

    test('应该能够验证邮箱格式', () => {
      expect(UserSystemUtils.isValidEmail('<EMAIL>')).toBe(true);
      expect(UserSystemUtils.isValidEmail('invalid-email')).toBe(false);
      expect(UserSystemUtils.isValidEmail('test@')).toBe(false);
    });

    test('应该能够验证用户名格式', () => {
      expect(UserSystemUtils.isValidUsername('validuser123')).toBe(true);
      expect(UserSystemUtils.isValidUsername('valid_user')).toBe(true);
      expect(UserSystemUtils.isValidUsername('valid-user')).toBe(true);
      expect(UserSystemUtils.isValidUsername('ab')).toBe(false); // 太短
      expect(UserSystemUtils.isValidUsername('user with spaces')).toBe(false); // 包含空格
      expect(UserSystemUtils.isValidUsername('user@domain')).toBe(false); // 包含特殊字符
    });

    test('应该能够格式化显示名称', () => {
      expect(UserSystemUtils.formatDisplayName({
        displayName: 'John Doe',
        username: 'johndoe',
        email: '<EMAIL>',
      })).toBe('John Doe');

      expect(UserSystemUtils.formatDisplayName({
        username: 'johndoe',
        email: '<EMAIL>',
      })).toBe('johndoe');

      expect(UserSystemUtils.formatDisplayName({
        email: '<EMAIL>',
      })).toBe('john');
    });
  });

  describe('系统健康检查', () => {
    test('应该返回健康状态', async () => {
      const health = await userSystem.healthCheck();

      expect(health.status).toBe('healthy');
      expect(health.database).toBe('connected');
      expect(health.userCount).toBe(0);
      expect(health.timestamp).toBeInstanceOf(Date);
    });

    test('应该返回系统统计信息', async () => {
      // 创建一些测试用户
      await userService.createUser({
        email: '<EMAIL>',
        authMethod: AuthMethod.EMAIL_PASSWORD,
      });

      await userService.createUser({
        email: '<EMAIL>',
        authMethod: AuthMethod.OAUTH_GOOGLE,
      });

      const stats = await userSystem.getStatistics();

      expect(stats.totalUsers).toBe(2);
      expect(stats.activeUsers).toBe(1); // OAuth用户默认激活
      expect(stats.usersByState[UserState.ACTIVE]).toBe(1);
      expect(stats.usersByState[UserState.PENDING]).toBe(1);
      expect(stats.usersByRole[UserRole.USER]).toBe(2);
    });
  });
});
