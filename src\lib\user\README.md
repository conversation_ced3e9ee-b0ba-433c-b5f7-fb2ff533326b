# 用户系统 - 基于易经"变化适应"智慧

> "天行健，君子以自强不息" - 根据情况灵活调整策略  
> "穷则变，变则通，通则久" - 体现变化性、适应性、平衡性的设计理念

## 🌟 核心特性

### 变化性 (Adaptability)
- **灵活的状态管理**: 基于易经八卦理念的用户状态转换
- **动态权限调整**: 支持实时权限授予和撤销
- **可扩展的认证方式**: 支持多种认证方法的无缝切换

### 适应性 (Flexibility)  
- **多数据库支持**: 抽象化的数据持久层，支持内存、MongoDB、PostgreSQL等
- **多认证方式**: 邮箱密码、OAuth、API Key、JWT等
- **可配置的业务规则**: 灵活的状态转换规则和权限策略

### 平衡性 (Balance)
- **安全与易用**: 在系统安全和用户体验之间保持动态平衡
- **性能与功能**: 高效的查询机制和完整的功能支持
- **简单与强大**: 简洁的API设计和强大的扩展能力

## 🏗️ 系统架构

```
用户系统架构
├── 类型定义层 (types.ts)
│   ├── 用户实体定义
│   ├── 状态和权限枚举
│   └── 接口和配置类型
├── 状态管理层 (state-manager.ts)
│   ├── 状态转换规则
│   ├── 易经变化逻辑
│   └── 状态历史记录
├── 认证授权层 (auth-manager.ts)
│   ├── 多方式认证
│   ├── 权限检查
│   └── 安全策略
├── 业务服务层 (user-service.ts)
│   ├── 用户CRUD操作
│   ├── 业务逻辑处理
│   └── 活动日志记录
├── 数据持久层 (repository.ts)
│   ├── 数据库抽象
│   ├── 查询优化
│   └── 批量操作
└── 系统入口层 (index.ts)
    ├── 工厂模式创建
    ├── 配置管理
    └── 工具函数
```

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 基础使用

```typescript
import { UserSystemFactory, AuthMethod, UserRole } from './src/lib/user';

// 创建开发环境用户系统
const userSystem = UserSystemFactory.createDevelopmentSystem('your-secret-key');
const userService = userSystem.users;

// 创建用户
const user = await userService.createUser({
  email: '<EMAIL>',
  password: 'SecurePass123!',
  username: 'johndoe',
  authMethod: AuthMethod.EMAIL_PASSWORD,
});

// 用户认证
const authResult = await userService.authenticate(
  '<EMAIL>',
  'SecurePass123!',
  AuthMethod.EMAIL_PASSWORD
);

if (authResult.success) {
  console.log('认证成功:', authResult.user);
}
```

### 生产环境配置

```typescript
const userSystem = UserSystemFactory.createProductionSystem({
  databaseUrl: 'mongodb://localhost:27017/myapp',
  databaseType: 'mongodb',
  jwtSecret: process.env.JWT_SECRET!,
  authConfig: {
    tokenExpiry: 3600, // 1小时
    maxLoginAttempts: 3,
    lockoutDuration: 30, // 30分钟
  },
});
```

## 📊 用户状态管理 - 易经变化理念

### 状态定义

| 状态 | 易经含义 | 描述 | 转换条件 |
|------|----------|------|----------|
| `INACTIVE` | 潜龙勿用 | 未激活状态 | 初始状态 |
| `PENDING` | 飞龙在天 | 待审核状态 | 需要验证 |
| `ACTIVE` | 见龙在田 | 活跃状态 | 正常使用 |
| `SUSPENDED` | 亢龙有悔 | 暂停状态 | 违规处理 |
| `ARCHIVED` | 群龙无首 | 归档状态 | 长期不活跃 |

### 状态转换示例

```typescript
// 激活用户 - 潜龙勿用 -> 见龙在田
await userService.changeUserState(
  userId,
  UserState.ACTIVE,
  '邮箱验证完成'
);

// 暂停用户 - 见龙在田 -> 亢龙有悔
await userService.changeUserState(
  userId,
  UserState.SUSPENDED,
  '违反社区规则',
  adminUser
);
```

## 🔐 权限管理系统

### 角色层次

```
SUPER_ADMIN (超级管理员)
    ↓
ADMIN (管理员)
    ↓
MODERATOR (版主)
    ↓
USER (普通用户)
    ↓
GUEST (访客)
```

### 权限操作

```typescript
// 授予权限
await userService.grantPermission(
  userId,
  Permission.MODERATE,
  adminUser,
  expiresAt // 可选的过期时间
);

// 撤销权限
await userService.revokePermission(
  userId,
  Permission.MODERATE,
  adminUser
);

// 权限检查
const hasPermission = authManager.hasPermission(user, Permission.WRITE);
```

## 🔍 查询和过滤

### 高级查询

```typescript
// 按条件查询用户
const result = await userService.queryUsers(
  {
    state: [UserState.ACTIVE, UserState.PENDING],
    roles: [UserRole.USER, UserRole.MODERATOR],
    createdAfter: new Date('2024-01-01'),
    tags: ['premium', 'verified'],
  },
  {
    limit: 20,
    offset: 0,
    sortBy: 'createdAt',
    sortOrder: 'desc',
    includeActivities: true,
  }
);
```

### 统计信息

```typescript
// 获取系统统计
const stats = await userSystem.getStatistics();
console.log({
  totalUsers: stats.totalUsers,
  activeUsers: stats.activeUsers,
  usersByState: stats.usersByState,
  usersByRole: stats.usersByRole,
});
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
npm test

# 运行用户系统测试
npm test src/lib/user/__tests__

# 运行测试并查看覆盖率
npm run test:coverage
```

### 测试示例

```typescript
describe('用户状态管理', () => {
  test('应该能够激活待审核用户', async () => {
    const user = await userService.createUser({
      email: '<EMAIL>',
      authMethod: AuthMethod.EMAIL_PASSWORD,
    });

    expect(user.state).toBe(UserState.PENDING);

    const updatedUser = await userService.changeUserState(
      user.profile.id,
      UserState.ACTIVE,
      '邮箱验证完成'
    );

    expect(updatedUser.state).toBe(UserState.ACTIVE);
  });
});
```

## 📚 API 文档

### UserService 主要方法

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `createUser()` | 创建新用户 | `CreateUserRequest` | `Promise<User>` |
| `authenticate()` | 用户认证 | `identifier, credentials, method` | `Promise<AuthResult>` |
| `getUserById()` | 根据ID获取用户 | `id, options?` | `Promise<User \| null>` |
| `updateUser()` | 更新用户信息 | `id, updates, currentUser?` | `Promise<User>` |
| `changeUserState()` | 更改用户状态 | `id, newState, reason?, currentUser?` | `Promise<User>` |
| `queryUsers()` | 查询用户列表 | `filter?, options?` | `Promise<QueryResult>` |

### 工具函数

```typescript
// 密码强度验证
const validation = UserSystemUtils.validatePassword('password123');

// 生成安全密码
const securePassword = UserSystemUtils.generateSecurePassword(12);

// 邮箱格式验证
const isValid = UserSystemUtils.isValidEmail('<EMAIL>');

// 用户名格式验证
const isValidUsername = UserSystemUtils.isValidUsername('johndoe');
```

## 🔧 配置选项

### 数据库配置

```typescript
interface DatabaseConfig {
  type: 'memory' | 'mongodb' | 'postgresql' | 'mysql' | 'sqlite';
  connectionString?: string;
  options?: Record<string, any>;
}
```

### 认证配置

```typescript
interface AuthConfig {
  jwtSecret: string;
  tokenExpiry: number;        // Token过期时间(秒)
  refreshTokenExpiry: number; // 刷新Token过期时间(秒)
  saltRounds: number;         // 密码加密轮数
  maxLoginAttempts: number;   // 最大登录尝试次数
  lockoutDuration: number;    // 锁定时长(分钟)
}
```

## 🌟 设计理念

### 易经智慧在系统中的体现

1. **变化性 (Change)**: 
   - 灵活的状态转换机制
   - 动态的权限调整能力
   - 可扩展的认证方式

2. **适应性 (Adaptation)**:
   - 多数据库支持
   - 可配置的业务规则
   - 模块化的架构设计

3. **平衡性 (Balance)**:
   - 安全性与易用性的平衡
   - 性能与功能的平衡
   - 简单性与强大性的平衡

4. **持续性 (Continuity)**:
   - 完整的审计日志
   - 状态变化历史
   - 系统监控和统计

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](../../../LICENSE) 文件

---

> 💡 **设计哲学**: 这个用户系统不仅仅是代码的实现，更是易经"变化适应"智慧在现代软件开发中的具体体现。通过灵活的架构设计和深思熟虑的业务逻辑，我们创造了一个既强大又优雅的用户管理解决方案。
