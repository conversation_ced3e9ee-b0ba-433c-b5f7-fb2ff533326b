/**
 * 用户系统类型定义
 * 基于易经"变化适应"智慧设计的灵活用户系统
 */

// 用户状态枚举 - 体现易经变化理念
export enum UserState {
  INACTIVE = 'inactive',      // 潜龙勿用 - 未激活状态
  ACTIVE = 'active',          // 见龙在田 - 活跃状态
  SUSPENDED = 'suspended',    // 亢龙有悔 - 暂停状态
  ARCHIVED = 'archived',      // 群龙无首 - 归档状态
  PENDING = 'pending',        // 飞龙在天 - 待审核状态
}

// 用户角色枚举
export enum UserRole {
  GUEST = 'guest',
  USER = 'user',
  MODERATOR = 'moderator',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}

// 权限枚举
export enum Permission {
  READ = 'read',
  WRITE = 'write',
  DELETE = 'delete',
  ADMIN = 'admin',
  MODERATE = 'moderate',
}

// 认证方式枚举
export enum AuthMethod {
  EMAIL_PASSWORD = 'email_password',
  OAUTH_GOOGLE = 'oauth_google',
  OAUTH_GITHUB = 'oauth_github',
  API_KEY = 'api_key',
  JWT = 'jwt',
}

// 用户基础信息接口
export interface UserProfile {
  id: string;
  email: string;
  username?: string;
  displayName?: string;
  avatar?: string;
  bio?: string;
  location?: string;
  website?: string;
  phone?: string;
  dateOfBirth?: Date;
  language?: string;
  timezone?: string;
}

// 用户认证信息
export interface UserAuth {
  id: string;
  userId: string;
  method: AuthMethod;
  identifier: string; // email, oauth_id, etc.
  credentials?: string; // hashed password, token, etc.
  isVerified: boolean;
  lastUsed?: Date;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

// 用户权限信息
export interface UserPermissions {
  roles: UserRole[];
  permissions: Permission[];
  customPermissions?: string[];
  restrictions?: string[];
  expiresAt?: Date;
}

// 用户状态变化记录
export interface UserStateTransition {
  id: string;
  userId: string;
  fromState: UserState;
  toState: UserState;
  reason?: string;
  triggeredBy?: string; // user id who triggered the change
  timestamp: Date;
  metadata?: Record<string, any>;
}

// 用户活动记录
export interface UserActivity {
  id: string;
  userId: string;
  action: string;
  resource?: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

// 用户偏好设置
export interface UserPreferences {
  theme?: 'light' | 'dark' | 'auto';
  notifications?: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  privacy?: {
    profileVisibility: 'public' | 'private' | 'friends';
    showEmail: boolean;
    showPhone: boolean;
  };
  customSettings?: Record<string, any>;
}

// 完整用户实体
export interface User {
  // 基础信息
  profile: UserProfile;
  
  // 状态管理
  state: UserState;
  stateHistory: UserStateTransition[];
  
  // 认证和权限
  auth: UserAuth[];
  permissions: UserPermissions;
  
  // 时间戳
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  lastActiveAt?: Date;
  
  // 扩展属性 - 体现"变则通"的灵活性
  preferences?: UserPreferences;
  metadata?: Record<string, any>;
  tags?: string[];
  
  // 关联数据
  activities?: UserActivity[];
}

// 用户创建请求
export interface CreateUserRequest {
  email: string;
  password?: string;
  username?: string;
  displayName?: string;
  authMethod: AuthMethod;
  initialRole?: UserRole;
  metadata?: Record<string, any>;
}

// 用户更新请求
export interface UpdateUserRequest {
  profile?: Partial<UserProfile>;
  preferences?: Partial<UserPreferences>;
  metadata?: Record<string, any>;
}

// 用户查询选项
export interface UserQueryOptions {
  includeAuth?: boolean;
  includeActivities?: boolean;
  includeStateHistory?: boolean;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 用户过滤条件
export interface UserFilter {
  state?: UserState[];
  roles?: UserRole[];
  createdAfter?: Date;
  createdBefore?: Date;
  lastActiveAfter?: Date;
  lastActiveBefore?: Date;
  tags?: string[];
  metadata?: Record<string, any>;
}

// 状态转换规则
export interface StateTransitionRule {
  from: UserState;
  to: UserState;
  requiredPermission?: Permission;
  requiredRole?: UserRole;
  validator?: (user: User, context?: any) => boolean;
  onTransition?: (user: User, context?: any) => Promise<void>;
}

// 用户系统配置
export interface UserSystemConfig {
  defaultRole: UserRole;
  defaultState: UserState;
  stateTransitionRules: StateTransitionRule[];
  authMethods: AuthMethod[];
  passwordPolicy?: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
  };
  sessionConfig?: {
    maxAge: number;
    renewThreshold: number;
  };
}

// 错误类型
export class UserSystemError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'UserSystemError';
  }
}

// 常用错误代码
export const UserErrorCodes = {
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  INVALID_STATE_TRANSITION: 'INVALID_STATE_TRANSITION',
  EMAIL_ALREADY_EXISTS: 'EMAIL_ALREADY_EXISTS',
  USERNAME_ALREADY_EXISTS: 'USERNAME_ALREADY_EXISTS',
  WEAK_PASSWORD: 'WEAK_PASSWORD',
  AUTH_METHOD_NOT_SUPPORTED: 'AUTH_METHOD_NOT_SUPPORTED',
} as const;

export type UserErrorCode = typeof UserErrorCodes[keyof typeof UserErrorCodes];
