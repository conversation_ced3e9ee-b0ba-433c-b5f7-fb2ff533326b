/**
 * 用户系统使用示例
 * 展示如何运用易经"变化适应"智慧进行用户管理
 */

import {
  UserSystemFactory,
  UserSystem,
  UserState,
  UserRole,
  AuthMethod,
  Permission,
  CreateUserRequest,
  UserSystemUtils,
} from '../index';

/**
 * 基础使用示例
 */
async function basicUsageExample() {
  console.log('=== 基础用户系统使用示例 ===');

  // 1. 创建用户系统实例
  const userSystem = UserSystemFactory.createDevelopmentSystem('my-secret-key');
  const userService = userSystem.users;

  // 2. 创建用户
  console.log('\n1. 创建用户');
  const newUser = await userService.createUser({
    email: '<EMAIL>',
    password: 'SecurePassword123!',
    username: 'johndoe',
    displayName: '<PERSON>',
    authMethod: AuthMethod.EMAIL_PASSWORD,
    metadata: {
      source: 'web_registration',
      referrer: 'google',
    },
  });

  console.log('用户创建成功:', {
    id: newUser.profile.id,
    email: newUser.profile.email,
    state: newUser.state,
    roles: newUser.permissions.roles,
  });

  // 3. 用户认证
  console.log('\n2. 用户认证');
  
  // 首先激活用户（模拟邮箱验证）
  await userService.changeUserState(newUser.profile.id, UserState.ACTIVE, '邮箱验证完成');
  
  const authResult = await userService.authenticate(
    '<EMAIL>',
    'SecurePassword123!',
    AuthMethod.EMAIL_PASSWORD
  );

  if (authResult.success) {
    console.log('认证成功:', {
      userId: authResult.user?.profile.id,
      token: authResult.token?.substring(0, 20) + '...',
      expiresAt: authResult.expiresAt,
    });
  } else {
    console.log('认证失败:', authResult.error);
  }

  // 4. 查询用户
  console.log('\n3. 查询用户');
  const foundUser = await userService.getUserByEmail('<EMAIL>');
  console.log('查询到的用户:', {
    id: foundUser?.profile.id,
    displayName: foundUser?.profile.displayName,
    state: foundUser?.state,
  });

  // 5. 更新用户信息
  console.log('\n4. 更新用户信息');
  const updatedUser = await userService.updateUser(newUser.profile.id, {
    profile: {
      bio: '软件工程师，热爱编程',
      location: '北京',
    },
    preferences: {
      theme: 'dark',
      notifications: {
        email: true,
        push: false,
        sms: false,
      },
    },
  });

  console.log('用户更新成功:', {
    bio: updatedUser.profile.bio,
    location: updatedUser.profile.location,
    theme: updatedUser.preferences?.theme,
  });

  return userSystem;
}

/**
 * 状态管理示例 - 体现易经变化理念
 */
async function stateManagementExample() {
  console.log('\n=== 用户状态管理示例 - 易经变化理念 ===');

  const userSystem = UserSystemFactory.createDevelopmentSystem('state-demo-key');
  const userService = userSystem.users;

  // 创建管理员用户
  const admin = await userService.createUser({
    email: '<EMAIL>',
    password: 'AdminPass123!',
    authMethod: AuthMethod.EMAIL_PASSWORD,
    initialRole: UserRole.ADMIN,
  });
  await userService.changeUserState(admin.profile.id, UserState.ACTIVE);

  // 创建普通用户
  const user = await userService.createUser({
    email: '<EMAIL>',
    password: 'UserPass123!',
    authMethod: AuthMethod.EMAIL_PASSWORD,
  });

  console.log('\n1. 初始状态 - 潜龙勿用');
  console.log('用户状态:', user.state); // PENDING

  console.log('\n2. 激活用户 - 见龙在田');
  await userService.changeUserState(
    user.profile.id,
    UserState.ACTIVE,
    '邮箱验证完成',
    admin
  );

  console.log('\n3. 暂停用户 - 亢龙有悔');
  await userService.changeUserState(
    user.profile.id,
    UserState.SUSPENDED,
    '违反社区规则',
    admin
  );

  console.log('\n4. 恢复用户 - 重新见龙在田');
  // 等待一段时间后恢复（模拟暂停期满）
  await userService.changeUserState(
    user.profile.id,
    UserState.ACTIVE,
    '暂停期满，恢复正常',
    admin
  );

  console.log('\n5. 归档用户 - 群龙无首');
  await userService.changeUserState(
    user.profile.id,
    UserState.ARCHIVED,
    '用户长期不活跃',
    admin
  );

  // 查看状态变化历史
  const finalUser = await userService.getUserById(user.profile.id);
  console.log('\n状态变化历史:');
  finalUser?.stateHistory.forEach((transition, index) => {
    console.log(`${index + 1}. ${transition.fromState} -> ${transition.toState}: ${transition.reason}`);
  });
}

/**
 * 权限管理示例 - 体现平衡性原则
 */
async function permissionManagementExample() {
  console.log('\n=== 权限管理示例 - 平衡性原则 ===');

  const userSystem = UserSystemFactory.createDevelopmentSystem('permission-demo-key');
  const userService = userSystem.users;

  // 创建不同角色的用户
  const superAdmin = await userService.createUser({
    email: '<EMAIL>',
    password: 'SuperPass123!',
    authMethod: AuthMethod.EMAIL_PASSWORD,
    initialRole: UserRole.SUPER_ADMIN,
  });

  const moderator = await userService.createUser({
    email: '<EMAIL>',
    password: 'ModPass123!',
    authMethod: AuthMethod.EMAIL_PASSWORD,
    initialRole: UserRole.MODERATOR,
  });

  const regularUser = await userService.createUser({
    email: '<EMAIL>',
    password: 'UserPass123!',
    authMethod: AuthMethod.EMAIL_PASSWORD,
    initialRole: UserRole.USER,
  });

  // 激活所有用户
  await userService.changeUserState(superAdmin.profile.id, UserState.ACTIVE);
  await userService.changeUserState(moderator.profile.id, UserState.ACTIVE);
  await userService.changeUserState(regularUser.profile.id, UserState.ACTIVE);

  console.log('\n1. 授予特殊权限');
  await userService.grantPermission(
    regularUser.profile.id,
    Permission.MODERATE,
    superAdmin,
    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天后过期
  );

  console.log('已授予普通用户临时管理权限');

  console.log('\n2. 权限检查');
  const updatedUser = await userService.getUserById(regularUser.profile.id);
  console.log('用户权限:', updatedUser?.permissions);

  console.log('\n3. 撤销权限');
  await userService.revokePermission(
    regularUser.profile.id,
    Permission.MODERATE,
    superAdmin
  );

  console.log('已撤销管理权限');
}

/**
 * 批量操作示例
 */
async function batchOperationsExample() {
  console.log('\n=== 批量操作示例 ===');

  const userSystem = UserSystemFactory.createDevelopmentSystem('batch-demo-key');
  const userService = userSystem.users;

  // 批量创建用户
  console.log('\n1. 批量创建用户');
  const userRequests: CreateUserRequest[] = [
    {
      email: '<EMAIL>',
      username: 'batch1',
      authMethod: AuthMethod.OAUTH_GOOGLE,
    },
    {
      email: '<EMAIL>',
      username: 'batch2',
      authMethod: AuthMethod.OAUTH_GITHUB,
    },
    {
      email: '<EMAIL>',
      username: 'batch3',
      authMethod: AuthMethod.EMAIL_PASSWORD,
      password: 'BatchPass123!',
    },
  ];

  const createdUsers = [];
  for (const request of userRequests) {
    const user = await userService.createUser(request);
    createdUsers.push(user);
  }

  console.log(`成功创建 ${createdUsers.length} 个用户`);

  // 批量查询
  console.log('\n2. 批量查询用户');
  const queryResult = await userService.queryUsers(
    { state: [UserState.ACTIVE, UserState.PENDING] },
    { limit: 10, sortBy: 'createdAt', sortOrder: 'desc' }
  );

  console.log(`查询到 ${queryResult.users.length} 个用户，总计 ${queryResult.total} 个`);
  queryResult.users.forEach(user => {
    console.log(`- ${user.profile.email} (${user.state})`);
  });
}

/**
 * 系统监控示例
 */
async function systemMonitoringExample() {
  console.log('\n=== 系统监控示例 ===');

  const userSystem = UserSystemFactory.createDevelopmentSystem('monitor-demo-key');

  // 创建一些测试数据
  const userService = userSystem.users;
  await userService.createUser({
    email: '<EMAIL>',
    authMethod: AuthMethod.EMAIL_PASSWORD,
  });

  await userService.createUser({
    email: '<EMAIL>',
    authMethod: AuthMethod.OAUTH_GOOGLE,
  });

  // 健康检查
  console.log('\n1. 系统健康检查');
  const health = await userSystem.healthCheck();
  console.log('健康状态:', health);

  // 系统统计
  console.log('\n2. 系统统计信息');
  const stats = await userSystem.getStatistics();
  console.log('统计信息:', {
    totalUsers: stats.totalUsers,
    activeUsers: stats.activeUsers,
    newUsersToday: stats.newUsersToday,
    usersByState: stats.usersByState,
    usersByRole: stats.usersByRole,
  });
}

/**
 * 工具函数示例
 */
function utilityFunctionsExample() {
  console.log('\n=== 工具函数示例 ===');

  console.log('\n1. 密码强度验证');
  const passwords = ['123', 'password', 'SecurePass123!'];
  passwords.forEach(pwd => {
    const validation = UserSystemUtils.validatePassword(pwd);
    console.log(`密码 "${pwd}": ${validation.isValid ? '✓ 有效' : '✗ 无效'}`);
    if (!validation.isValid) {
      console.log('  错误:', validation.errors.join(', '));
    }
  });

  console.log('\n2. 生成安全密码');
  const securePassword = UserSystemUtils.generateSecurePassword(16);
  console.log('生成的安全密码:', securePassword);

  console.log('\n3. 邮箱格式验证');
  const emails = ['<EMAIL>', 'invalid-email', 'test@'];
  emails.forEach(email => {
    const isValid = UserSystemUtils.isValidEmail(email);
    console.log(`邮箱 "${email}": ${isValid ? '✓ 有效' : '✗ 无效'}`);
  });

  console.log('\n4. 用户名格式验证');
  const usernames = ['validuser', 'valid_user', 'ab', 'user with spaces'];
  usernames.forEach(username => {
    const isValid = UserSystemUtils.isValidUsername(username);
    console.log(`用户名 "${username}": ${isValid ? '✓ 有效' : '✗ 无效'}`);
  });
}

/**
 * 主函数 - 运行所有示例
 */
async function runAllExamples() {
  try {
    console.log('🌟 用户系统示例 - 基于易经"变化适应"智慧 🌟');
    
    await basicUsageExample();
    await stateManagementExample();
    await permissionManagementExample();
    await batchOperationsExample();
    await systemMonitoringExample();
    utilityFunctionsExample();

    console.log('\n✅ 所有示例运行完成！');
    console.log('\n💡 这个用户系统体现了易经的核心智慧：');
    console.log('   - 变化性：灵活的状态转换和权限调整');
    console.log('   - 适应性：支持多种认证方式和存储后端');
    console.log('   - 平衡性：在安全性和易用性之间保持动态平衡');
    console.log('   - 持续性：完整的审计日志和状态历史');

  } catch (error) {
    console.error('❌ 示例运行出错:', error);
  }
}

// 如果直接运行此文件，则执行所有示例
if (require.main === module) {
  runAllExamples();
}

export {
  basicUsageExample,
  stateManagementExample,
  permissionManagementExample,
  batchOperationsExample,
  systemMonitoringExample,
  utilityFunctionsExample,
  runAllExamples,
};
