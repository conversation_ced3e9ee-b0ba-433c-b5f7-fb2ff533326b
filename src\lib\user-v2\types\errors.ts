/**
 * 用户系统v2 - 错误类型定义
 * 基于道德经"知其雄，守其雌"原则 - 预见问题，优雅处理
 * 
 * <AUTHOR>
 * @version 2.0.0
 */

// ============================================================================
// 错误代码枚举 - 统一错误标识
// ============================================================================

/**
 * 用户系统错误代码
 */
export enum UserErrorCode {
  // 通用错误 (1000-1099)
  UNKNOWN_ERROR = 'USER_1000',
  INVALID_INPUT = 'USER_1001',
  VALIDATION_FAILED = 'USER_1002',
  OPERATION_FAILED = 'USER_1003',
  
  // 用户相关错误 (1100-1199)
  USER_NOT_FOUND = 'USER_1100',
  USER_ALREADY_EXISTS = 'USER_1101',
  USER_INACTIVE = 'USER_1102',
  USER_SUSPENDED = 'USER_1103',
  USER_DELETED = 'USER_1104',
  
  // 认证相关错误 (1200-1299)
  AUTH_FAILED = 'USER_1200',
  INVALID_CREDENTIALS = 'USER_1201',
  TOKEN_EXPIRED = 'USER_1202',
  TOKEN_INVALID = 'USER_1203',
  ACCOUNT_LOCKED = 'USER_1204',
  TOO_MANY_ATTEMPTS = 'USER_1205',
  AUTH_METHOD_NOT_SUPPORTED = 'USER_1206',
  
  // 权限相关错误 (1300-1399)
  PERMISSION_DENIED = 'USER_1300',
  INSUFFICIENT_PRIVILEGES = 'USER_1301',
  ROLE_NOT_FOUND = 'USER_1302',
  PERMISSION_EXPIRED = 'USER_1303',
  
  // 数据相关错误 (1400-1499)
  DATABASE_ERROR = 'USER_1400',
  CONNECTION_FAILED = 'USER_1401',
  QUERY_FAILED = 'USER_1402',
  TRANSACTION_FAILED = 'USER_1403',
  
  // 配置相关错误 (1500-1599)
  CONFIG_ERROR = 'USER_1500',
  INVALID_CONFIG = 'USER_1501',
  MISSING_CONFIG = 'USER_1502',
  
  // 系统相关错误 (1600-1699)
  SYSTEM_ERROR = 'USER_1600',
  SERVICE_UNAVAILABLE = 'USER_1601',
  RATE_LIMIT_EXCEEDED = 'USER_1602',
  MAINTENANCE_MODE = 'USER_1603'
}

// ============================================================================
// 错误严重级别
// ============================================================================

/**
 * 错误严重级别
 */
export enum ErrorSeverity {
  LOW = 'low',           // 低级别 - 用户可恢复
  MEDIUM = 'medium',     // 中级别 - 需要用户干预
  HIGH = 'high',         // 高级别 - 系统功能受影响
  CRITICAL = 'critical'  // 严重级别 - 系统不可用
}

// ============================================================================
// 错误接口定义
// ============================================================================

/**
 * 基础错误信息
 */
export interface ErrorInfo {
  readonly code: UserErrorCode;
  readonly message: string;
  readonly severity: ErrorSeverity;
  readonly timestamp: Date;
  readonly context?: Record<string, unknown>;
  readonly stack?: string;
}

/**
 * 用户系统错误类
 * 体现"柔弱胜刚强"原则 - 优雅的错误处理
 */
export class UserSystemError extends Error {
  public readonly code: UserErrorCode;
  public readonly severity: ErrorSeverity;
  public readonly timestamp: Date;
  public readonly context?: Record<string, unknown>;

  constructor(
    message: string,
    code: UserErrorCode = UserErrorCode.UNKNOWN_ERROR,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'UserSystemError';
    this.code = code;
    this.severity = severity;
    this.timestamp = new Date();
    this.context = context;

    // 保持错误堆栈
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, UserSystemError);
    }
  }

  /**
   * 转换为错误信息对象
   */
  toErrorInfo(): ErrorInfo {
    return {
      code: this.code,
      message: this.message,
      severity: this.severity,
      timestamp: this.timestamp,
      context: this.context,
      stack: this.stack
    };
  }

  /**
   * 转换为JSON格式
   */
  toJSON(): Record<string, unknown> {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      severity: this.severity,
      timestamp: this.timestamp.toISOString(),
      context: this.context
    };
  }

  /**
   * 检查是否为特定错误代码
   */
  is(code: UserErrorCode): boolean {
    return this.code === code;
  }

  /**
   * 检查是否为特定严重级别
   */
  hasSeverity(severity: ErrorSeverity): boolean {
    return this.severity === severity;
  }
}

// ============================================================================
// 错误工厂函数 - 简化错误创建
// ============================================================================

/**
 * 错误工厂类
 * 体现"无为而治"原则 - 简化错误创建过程
 */
export class ErrorFactory {
  /**
   * 创建用户不存在错误
   */
  static userNotFound(userId: string): UserSystemError {
    return new UserSystemError(
      `User not found: ${userId}`,
      UserErrorCode.USER_NOT_FOUND,
      ErrorSeverity.LOW,
      { userId }
    );
  }

  /**
   * 创建认证失败错误
   */
  static authenticationFailed(reason?: string): UserSystemError {
    return new UserSystemError(
      `Authentication failed${reason ? `: ${reason}` : ''}`,
      UserErrorCode.AUTH_FAILED,
      ErrorSeverity.MEDIUM,
      { reason }
    );
  }

  /**
   * 创建权限不足错误
   */
  static permissionDenied(action: string, userId?: string): UserSystemError {
    return new UserSystemError(
      `Permission denied for action: ${action}`,
      UserErrorCode.PERMISSION_DENIED,
      ErrorSeverity.MEDIUM,
      { action, userId }
    );
  }

  /**
   * 创建验证失败错误
   */
  static validationFailed(field: string, value: unknown, rule: string): UserSystemError {
    return new UserSystemError(
      `Validation failed for field '${field}': ${rule}`,
      UserErrorCode.VALIDATION_FAILED,
      ErrorSeverity.LOW,
      { field, value, rule }
    );
  }

  /**
   * 创建数据库错误
   */
  static databaseError(operation: string, error: Error): UserSystemError {
    return new UserSystemError(
      `Database operation failed: ${operation}`,
      UserErrorCode.DATABASE_ERROR,
      ErrorSeverity.HIGH,
      { operation, originalError: error.message }
    );
  }

  /**
   * 创建配置错误
   */
  static configError(configKey: string, reason: string): UserSystemError {
    return new UserSystemError(
      `Configuration error for '${configKey}': ${reason}`,
      UserErrorCode.CONFIG_ERROR,
      ErrorSeverity.HIGH,
      { configKey, reason }
    );
  }

  /**
   * 创建系统错误
   */
  static systemError(message: string, context?: Record<string, unknown>): UserSystemError {
    return new UserSystemError(
      message,
      UserErrorCode.SYSTEM_ERROR,
      ErrorSeverity.CRITICAL,
      context
    );
  }
}

// ============================================================================
// 错误处理工具
// ============================================================================

/**
 * 错误处理工具类
 */
export class ErrorHandler {
  /**
   * 判断是否为用户系统错误
   */
  static isUserSystemError(error: unknown): error is UserSystemError {
    return error instanceof UserSystemError;
  }

  /**
   * 安全地提取错误信息
   */
  static extractErrorInfo(error: unknown): ErrorInfo {
    if (this.isUserSystemError(error)) {
      return error.toErrorInfo();
    }

    if (error instanceof Error) {
      return {
        code: UserErrorCode.UNKNOWN_ERROR,
        message: error.message,
        severity: ErrorSeverity.MEDIUM,
        timestamp: new Date(),
        stack: error.stack
      };
    }

    return {
      code: UserErrorCode.UNKNOWN_ERROR,
      message: String(error),
      severity: ErrorSeverity.MEDIUM,
      timestamp: new Date()
    };
  }

  /**
   * 格式化错误消息
   */
  static formatError(error: unknown): string {
    const info = this.extractErrorInfo(error);
    return `[${info.code}] ${info.message}`;
  }

  /**
   * 检查错误是否可重试
   */
  static isRetryable(error: unknown): boolean {
    if (!this.isUserSystemError(error)) {
      return false;
    }

    const retryableCodes = [
      UserErrorCode.DATABASE_ERROR,
      UserErrorCode.CONNECTION_FAILED,
      UserErrorCode.SERVICE_UNAVAILABLE
    ];

    return retryableCodes.includes(error.code);
  }
}

// ============================================================================
// 类型导出
// ============================================================================

export type { ErrorInfo };
