/**
 * 用户系统v2 - 认证提供者
 * 基于道德经"知其雄，守其雌"原则 - 强大功能，简洁接口
 * 
 * <AUTHOR>
 * @version 2.0.0
 */

import {
  AuthMethod,
  AuthResult,
  User,
  UserAuth,
  AuthConfig,
  UserSystemError,
  ErrorFactory,
  UserErrorCode
} from '../types';

// ============================================================================
// 认证提供者接口 - 统一认证抽象
// ============================================================================

/**
 * 认证提供者基础接口
 * 体现"道生一"的统一性
 */
export interface IAuthProvider {
  readonly method: AuthMethod;
  
  /**
   * 认证用户
   */
  authenticate(identifier: string, credentials: string): Promise<AuthResult>;
  
  /**
   * 验证凭据
   */
  validateCredentials(userAuth: UserAuth, credentials: string): Promise<boolean>;
  
  /**
   * 生成认证信息
   */
  generateAuth(identifier: string, credentials: string): Promise<UserAuth>;
  
  /**
   * 刷新认证
   */
  refreshAuth?(userAuth: UserAuth): Promise<UserAuth>;
  
  /**
   * 撤销认证
   */
  revokeAuth?(userAuth: UserAuth): Promise<void>;
}

// ============================================================================
// 邮箱密码认证提供者
// ============================================================================

/**
 * 邮箱密码认证提供者
 * 体现"朴素"原则 - 最基础的认证方式
 */
export class EmailPasswordProvider implements IAuthProvider {
  readonly method = AuthMethod.EMAIL;

  constructor(
    private readonly config: AuthConfig,
    private readonly findUserByEmail: (email: string) => Promise<User | null>,
    private readonly hashPassword: (password: string) => Promise<string>,
    private readonly verifyPassword: (password: string, hash: string) => Promise<boolean>
  ) {}

  async authenticate(email: string, password: string): Promise<AuthResult> {
    try {
      // 查找用户
      const user = await this.findUserByEmail(email);
      if (!user) {
        return {
          success: false,
          error: 'Invalid email or password'
        };
      }

      // 查找邮箱认证信息
      const emailAuth = user.auth.find(auth => 
        auth.method === AuthMethod.EMAIL && auth.identifier === email
      );

      if (!emailAuth || !emailAuth.isVerified) {
        return {
          success: false,
          error: 'Email not verified'
        };
      }

      // 验证密码
      const isValid = await this.validateCredentials(emailAuth, password);
      if (!isValid) {
        return {
          success: false,
          error: 'Invalid email or password'
        };
      }

      return {
        success: true,
        user
      };
    } catch (error) {
      throw ErrorFactory.authenticationFailed(
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }

  async validateCredentials(userAuth: UserAuth, password: string): Promise<boolean> {
    if (!userAuth.credentials) {
      return false;
    }

    return this.verifyPassword(password, userAuth.credentials);
  }

  async generateAuth(email: string, password: string): Promise<UserAuth> {
    const hashedPassword = await this.hashPassword(password);
    
    return {
      method: AuthMethod.EMAIL,
      identifier: email,
      credentials: hashedPassword,
      isVerified: false, // 需要邮箱验证
      metadata: {
        createdAt: new Date().toISOString()
      }
    };
  }
}

// ============================================================================
// JWT认证提供者
// ============================================================================

/**
 * JWT认证提供者
 * 体现"无为而治"原则 - 无状态认证
 */
export class JwtProvider implements IAuthProvider {
  readonly method = AuthMethod.JWT;

  constructor(
    private readonly config: AuthConfig,
    private readonly findUserById: (id: string) => Promise<User | null>,
    private readonly generateJWT: (payload: any) => string,
    private readonly verifyJWT: (token: string) => any
  ) {}

  async authenticate(userId: string, token: string): Promise<AuthResult> {
    try {
      // 验证JWT
      const payload = this.verifyJWT(token);
      if (!payload || payload.userId !== userId) {
        return {
          success: false,
          error: 'Invalid token'
        };
      }

      // 检查过期时间
      if (payload.exp && Date.now() >= payload.exp * 1000) {
        return {
          success: false,
          error: 'Token expired'
        };
      }

      // 查找用户
      const user = await this.findUserById(userId);
      if (!user) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      return {
        success: true,
        user,
        token,
        expiresAt: payload.exp ? new Date(payload.exp * 1000) : undefined
      };
    } catch (error) {
      return {
        success: false,
        error: 'Invalid token'
      };
    }
  }

  async validateCredentials(userAuth: UserAuth, token: string): Promise<boolean> {
    try {
      const payload = this.verifyJWT(token);
      return !!payload && !this.isTokenExpired(payload);
    } catch {
      return false;
    }
  }

  async generateAuth(userId: string, _credentials: string): Promise<UserAuth> {
    const expiresAt = new Date(Date.now() + this.config.tokenExpiry * 1000);
    
    const payload = {
      userId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(expiresAt.getTime() / 1000)
    };

    const token = this.generateJWT(payload);

    return {
      method: AuthMethod.JWT,
      identifier: userId,
      credentials: token,
      isVerified: true,
      expiresAt,
      metadata: {
        tokenType: 'access',
        algorithm: 'HS256'
      }
    };
  }

  async refreshAuth(userAuth: UserAuth): Promise<UserAuth> {
    if (!userAuth.credentials) {
      throw new UserSystemError('No token to refresh', UserErrorCode.INVALID_INPUT);
    }

    try {
      const payload = this.verifyJWT(userAuth.credentials);
      if (!payload) {
        throw new UserSystemError('Invalid token', UserErrorCode.TOKEN_INVALID);
      }

      // 生成新token
      return this.generateAuth(payload.userId, '');
    } catch (error) {
      throw ErrorFactory.authenticationFailed('Token refresh failed');
    }
  }

  private isTokenExpired(payload: any): boolean {
    return payload.exp && Date.now() >= payload.exp * 1000;
  }
}

// ============================================================================
// API密钥认证提供者
// ============================================================================

/**
 * API密钥认证提供者
 * 体现"恒久"原则 - 长期有效的认证方式
 */
export class ApiKeyProvider implements IAuthProvider {
  readonly method = AuthMethod.API_KEY;

  constructor(
    private readonly findUserByApiKey: (apiKey: string) => Promise<User | null>,
    private readonly generateApiKey: () => string,
    private readonly hashApiKey: (apiKey: string) => Promise<string>
  ) {}

  async authenticate(userId: string, apiKey: string): Promise<AuthResult> {
    try {
      // 通过API密钥查找用户
      const user = await this.findUserByApiKey(apiKey);
      if (!user || user.identity.id !== userId) {
        return {
          success: false,
          error: 'Invalid API key'
        };
      }

      // 查找API密钥认证信息
      const apiKeyAuth = user.auth.find(auth => 
        auth.method === AuthMethod.API_KEY
      );

      if (!apiKeyAuth || !apiKeyAuth.isVerified) {
        return {
          success: false,
          error: 'API key not verified'
        };
      }

      // 检查过期时间
      if (apiKeyAuth.expiresAt && apiKeyAuth.expiresAt < new Date()) {
        return {
          success: false,
          error: 'API key expired'
        };
      }

      return {
        success: true,
        user
      };
    } catch (error) {
      throw ErrorFactory.authenticationFailed(
        error instanceof Error ? error.message : 'API key authentication failed'
      );
    }
  }

  async validateCredentials(userAuth: UserAuth, apiKey: string): Promise<boolean> {
    if (!userAuth.credentials) {
      return false;
    }

    // 简单比较（实际应用中应该使用哈希比较）
    return userAuth.credentials === apiKey;
  }

  async generateAuth(userId: string, _credentials: string): Promise<UserAuth> {
    const apiKey = this.generateApiKey();
    const hashedApiKey = await this.hashApiKey(apiKey);

    return {
      method: AuthMethod.API_KEY,
      identifier: userId,
      credentials: hashedApiKey,
      isVerified: true,
      metadata: {
        plainApiKey: apiKey, // 仅在生成时返回明文
        createdAt: new Date().toISOString()
      }
    };
  }

  async revokeAuth(userAuth: UserAuth): Promise<void> {
    // 标记为已撤销（实际实现中可能需要更新数据库）
    userAuth.metadata = {
      ...userAuth.metadata,
      revokedAt: new Date().toISOString()
    };
  }
}

// ============================================================================
// OAuth认证提供者
// ============================================================================

/**
 * OAuth认证提供者
 * 体现"借力"原则 - 借助第三方认证
 */
export class OAuthProvider implements IAuthProvider {
  readonly method = AuthMethod.OAUTH;

  constructor(
    private readonly provider: string, // 'google', 'github', etc.
    private readonly verifyOAuthToken: (token: string, provider: string) => Promise<any>,
    private readonly findUserByOAuthId: (oauthId: string, provider: string) => Promise<User | null>
  ) {}

  async authenticate(oauthId: string, token: string): Promise<AuthResult> {
    try {
      // 验证OAuth令牌
      const oauthData = await this.verifyOAuthToken(token, this.provider);
      if (!oauthData || oauthData.id !== oauthId) {
        return {
          success: false,
          error: 'Invalid OAuth token'
        };
      }

      // 查找用户
      const user = await this.findUserByOAuthId(oauthId, this.provider);
      if (!user) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      return {
        success: true,
        user
      };
    } catch (error) {
      throw ErrorFactory.authenticationFailed(
        `OAuth authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async validateCredentials(userAuth: UserAuth, token: string): Promise<boolean> {
    try {
      const oauthData = await this.verifyOAuthToken(token, this.provider);
      return !!oauthData && oauthData.id === userAuth.identifier;
    } catch {
      return false;
    }
  }

  async generateAuth(oauthId: string, token: string): Promise<UserAuth> {
    const oauthData = await this.verifyOAuthToken(token, this.provider);
    
    return {
      method: AuthMethod.OAUTH,
      identifier: oauthId,
      credentials: token,
      isVerified: true,
      metadata: {
        provider: this.provider,
        email: oauthData.email,
        name: oauthData.name,
        avatar: oauthData.avatar,
        verifiedAt: new Date().toISOString()
      }
    };
  }
}

// ============================================================================
// 生物识别认证提供者
// ============================================================================

/**
 * 生物识别认证提供者
 * 体现"天人合一"原则 - 人与技术的自然结合
 */
export class BiometricProvider implements IAuthProvider {
  readonly method = AuthMethod.BIOMETRIC;

  constructor(
    private readonly verifyBiometric: (userId: string, biometricData: string) => Promise<boolean>,
    private readonly findUserById: (id: string) => Promise<User | null>
  ) {}

  async authenticate(userId: string, biometricData: string): Promise<AuthResult> {
    try {
      // 查找用户
      const user = await this.findUserById(userId);
      if (!user) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      // 验证生物识别数据
      const isValid = await this.verifyBiometric(userId, biometricData);
      if (!isValid) {
        return {
          success: false,
          error: 'Biometric verification failed'
        };
      }

      return {
        success: true,
        user
      };
    } catch (error) {
      throw ErrorFactory.authenticationFailed(
        `Biometric authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async validateCredentials(userAuth: UserAuth, biometricData: string): Promise<boolean> {
    if (!userAuth.identifier) {
      return false;
    }

    return this.verifyBiometric(userAuth.identifier, biometricData);
  }

  async generateAuth(userId: string, biometricTemplate: string): Promise<UserAuth> {
    return {
      method: AuthMethod.BIOMETRIC,
      identifier: userId,
      credentials: biometricTemplate, // 生物识别模板
      isVerified: true,
      metadata: {
        biometricType: 'fingerprint', // 可扩展支持多种类型
        enrolledAt: new Date().toISOString()
      }
    };
  }
}

// ============================================================================
// 认证提供者工厂
// ============================================================================

/**
 * 认证提供者工厂
 * 体现"因材施教"原则 - 根据需要创建合适的提供者
 */
export class AuthProviderFactory {
  /**
   * 创建认证提供者
   */
  static createProvider(
    method: AuthMethod,
    config: AuthConfig,
    dependencies: any
  ): IAuthProvider {
    switch (method) {
      case AuthMethod.EMAIL:
        return new EmailPasswordProvider(
          config,
          dependencies.findUserByEmail,
          dependencies.hashPassword,
          dependencies.verifyPassword
        );

      case AuthMethod.JWT:
        return new JwtProvider(
          config,
          dependencies.findUserById,
          dependencies.generateJWT,
          dependencies.verifyJWT
        );

      case AuthMethod.API_KEY:
        return new ApiKeyProvider(
          dependencies.findUserByApiKey,
          dependencies.generateApiKey,
          dependencies.hashApiKey
        );

      case AuthMethod.OAUTH:
        return new OAuthProvider(
          dependencies.provider,
          dependencies.verifyOAuthToken,
          dependencies.findUserByOAuthId
        );

      case AuthMethod.BIOMETRIC:
        return new BiometricProvider(
          dependencies.verifyBiometric,
          dependencies.findUserById
        );

      default:
        throw new UserSystemError(
          `Unsupported authentication method: ${method}`,
          UserErrorCode.AUTH_METHOD_NOT_SUPPORTED
        );
    }
  }

  /**
   * 获取支持的认证方法
   */
  static getSupportedMethods(): AuthMethod[] {
    return [
      AuthMethod.EMAIL,
      AuthMethod.JWT,
      AuthMethod.API_KEY,
      AuthMethod.OAUTH,
      AuthMethod.BIOMETRIC
    ];
  }

  /**
   * 检查认证方法是否支持
   */
  static isMethodSupported(method: AuthMethod): boolean {
    return this.getSupportedMethods().includes(method);
  }
}
