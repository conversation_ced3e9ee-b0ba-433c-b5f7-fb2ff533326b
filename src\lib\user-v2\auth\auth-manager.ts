/**
 * 用户系统v2 - 认证管理器
 * 基于道德经"执大象，天下往"原则 - 统一管理，天下归心
 * 
 * <AUTHOR>
 * @version 2.0.0
 */

import {
  AuthMethod,
  AuthResult,
  User,
  UserAuth,
  AuthConfig,
  UserSystemError,
  ErrorFactory,
  UserErrorCode,
  Permission,
  UserRole
} from '../types';

import {
  IAuthProvider,
  AuthProviderFactory
} from './auth-provider';

// ============================================================================
// 认证管理器接口
// ============================================================================

/**
 * 认证管理器接口
 * 体现"道生一"的统一管理
 */
export interface IAuthManager {
  /**
   * 用户认证
   */
  authenticate(identifier: string, credentials: string, method: AuthMethod): Promise<AuthResult>;
  
  /**
   * 验证令牌
   */
  validateToken(token: string): Promise<AuthResult>;
  
  /**
   * 刷新令牌
   */
  refreshToken(refreshToken: string): Promise<AuthResult>;
  
  /**
   * 撤销认证
   */
  revokeAuth(userId: string, method?: AuthMethod): Promise<void>;
  
  /**
   * 权限检查
   */
  hasPermission(user: User, permission: Permission): boolean;
  
  /**
   * 角色检查
   */
  hasRole(user: User, role: UserRole): boolean;
}

// ============================================================================
// 认证管理器实现
// ============================================================================

/**
 * 认证管理器
 * 体现"无为而治"原则 - 简洁统一的认证管理
 */
export class AuthManager implements IAuthManager {
  private readonly providers: Map<AuthMethod, IAuthProvider> = new Map();
  private readonly loginAttempts: Map<string, { count: number; lastAttempt: Date }> = new Map();

  constructor(
    private readonly config: AuthConfig,
    private readonly dependencies: {
      findUserByEmail: (email: string) => Promise<User | null>;
      findUserById: (id: string) => Promise<User | null>;
      findUserByApiKey: (apiKey: string) => Promise<User | null>;
      findUserByOAuthId: (oauthId: string, provider: string) => Promise<User | null>;
      updateUser: (user: User) => Promise<void>;
      hashPassword: (password: string) => Promise<string>;
      verifyPassword: (password: string, hash: string) => Promise<boolean>;
      generateJWT: (payload: any) => string;
      verifyJWT: (token: string) => any;
      generateApiKey: () => string;
      hashApiKey: (apiKey: string) => Promise<string>;
      verifyOAuthToken: (token: string, provider: string) => Promise<any>;
      verifyBiometric?: (userId: string, biometricData: string) => Promise<boolean>;
    }
  ) {
    this.initializeProviders();
  }

  // ============================================================================
  // 认证方法
  // ============================================================================

  /**
   * 用户认证 - 支持多种认证方式
   */
  async authenticate(identifier: string, credentials: string, method: AuthMethod): Promise<AuthResult> {
    try {
      // 检查账户锁定状态
      if (this.isAccountLocked(identifier)) {
        return {
          success: false,
          error: 'Account temporarily locked due to too many failed attempts'
        };
      }

      // 获取认证提供者
      const provider = this.providers.get(method);
      if (!provider) {
        return {
          success: false,
          error: `Authentication method ${method} not supported`
        };
      }

      // 执行认证
      const result = await provider.authenticate(identifier, credentials);

      // 处理认证结果
      if (result.success) {
        this.clearLoginAttempts(identifier);
        await this.updateLastLogin(result.user!);
      } else {
        this.recordFailedAttempt(identifier);
      }

      return result;
    } catch (error) {
      this.recordFailedAttempt(identifier);
      
      if (error instanceof UserSystemError) {
        throw error;
      }
      
      throw ErrorFactory.authenticationFailed(
        error instanceof Error ? error.message : 'Unknown authentication error'
      );
    }
  }

  /**
   * 验证JWT令牌
   */
  async validateToken(token: string): Promise<AuthResult> {
    const jwtProvider = this.providers.get(AuthMethod.JWT);
    if (!jwtProvider) {
      return {
        success: false,
        error: 'JWT authentication not configured'
      };
    }

    try {
      // 解析令牌获取用户ID
      const payload = this.dependencies.verifyJWT(token);
      if (!payload || !payload.userId) {
        return {
          success: false,
          error: 'Invalid token'
        };
      }

      return jwtProvider.authenticate(payload.userId, token);
    } catch (error) {
      return {
        success: false,
        error: 'Token validation failed'
      };
    }
  }

  /**
   * 刷新令牌
   */
  async refreshToken(refreshToken: string): Promise<AuthResult> {
    const jwtProvider = this.providers.get(AuthMethod.JWT);
    if (!jwtProvider || !jwtProvider.refreshAuth) {
      return {
        success: false,
        error: 'Token refresh not supported'
      };
    }

    try {
      // 验证刷新令牌
      const payload = this.dependencies.verifyJWT(refreshToken);
      if (!payload || !payload.userId || payload.type !== 'refresh') {
        return {
          success: false,
          error: 'Invalid refresh token'
        };
      }

      // 查找用户
      const user = await this.dependencies.findUserById(payload.userId);
      if (!user) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      // 生成新的访问令牌
      const newAuth = await jwtProvider.generateAuth(user.identity.id, '');
      
      return {
        success: true,
        user,
        token: newAuth.credentials,
        expiresAt: newAuth.expiresAt
      };
    } catch (error) {
      return {
        success: false,
        error: 'Token refresh failed'
      };
    }
  }

  /**
   * 撤销认证
   */
  async revokeAuth(userId: string, method?: AuthMethod): Promise<void> {
    const user = await this.dependencies.findUserById(userId);
    if (!user) {
      throw ErrorFactory.userNotFound(userId);
    }

    if (method) {
      // 撤销特定方法的认证
      const provider = this.providers.get(method);
      const userAuth = user.auth.find(auth => auth.method === method);
      
      if (provider && provider.revokeAuth && userAuth) {
        await provider.revokeAuth(userAuth);
      }
    } else {
      // 撤销所有认证
      for (const userAuth of user.auth) {
        const provider = this.providers.get(userAuth.method);
        if (provider && provider.revokeAuth) {
          await provider.revokeAuth(userAuth);
        }
      }
    }

    await this.dependencies.updateUser(user);
  }

  // ============================================================================
  // 权限检查方法
  // ============================================================================

  /**
   * 检查用户权限
   */
  hasPermission(user: User, permission: Permission): boolean {
    // 检查直接权限
    if (user.permissions.permissions.includes(permission)) {
      return true;
    }

    // 检查角色权限
    return this.roleHasPermission(user.permissions.roles, permission);
  }

  /**
   * 检查用户角色
   */
  hasRole(user: User, role: UserRole): boolean {
    return user.permissions.roles.includes(role);
  }

  /**
   * 检查多个权限（全部）
   */
  hasAllPermissions(user: User, permissions: Permission[]): boolean {
    return permissions.every(permission => this.hasPermission(user, permission));
  }

  /**
   * 检查多个权限（任一）
   */
  hasAnyPermission(user: User, permissions: Permission[]): boolean {
    return permissions.some(permission => this.hasPermission(user, permission));
  }

  // ============================================================================
  // 私有辅助方法
  // ============================================================================

  /**
   * 初始化认证提供者
   */
  private initializeProviders(): void {
    const supportedMethods = AuthProviderFactory.getSupportedMethods();
    
    for (const method of supportedMethods) {
      try {
        const provider = AuthProviderFactory.createProvider(method, this.config, this.dependencies);
        this.providers.set(method, provider);
      } catch (error) {
        // 某些认证方法可能因为缺少依赖而无法初始化，这是正常的
        console.warn(`Failed to initialize auth provider for ${method}:`, error);
      }
    }
  }

  /**
   * 检查账户是否被锁定
   */
  private isAccountLocked(identifier: string): boolean {
    const attempts = this.loginAttempts.get(identifier);
    if (!attempts) {
      return false;
    }

    const lockoutTime = this.config.lockoutDuration * 60 * 1000; // 转换为毫秒
    const timeSinceLastAttempt = Date.now() - attempts.lastAttempt.getTime();

    if (timeSinceLastAttempt > lockoutTime) {
      // 锁定时间已过，清除记录
      this.loginAttempts.delete(identifier);
      return false;
    }

    return attempts.count >= this.config.maxLoginAttempts;
  }

  /**
   * 记录失败的登录尝试
   */
  private recordFailedAttempt(identifier: string): void {
    const attempts = this.loginAttempts.get(identifier) || { count: 0, lastAttempt: new Date() };
    attempts.count++;
    attempts.lastAttempt = new Date();
    this.loginAttempts.set(identifier, attempts);
  }

  /**
   * 清除登录尝试记录
   */
  private clearLoginAttempts(identifier: string): void {
    this.loginAttempts.delete(identifier);
  }

  /**
   * 更新最后登录时间
   */
  private async updateLastLogin(user: User): Promise<void> {
    // 更新用户的最后登录时间
    const updatedUser = {
      ...user,
      lastLoginAt: new Date(),
      updatedAt: new Date()
    };
    
    await this.dependencies.updateUser(updatedUser);
  }

  /**
   * 检查角色是否拥有权限
   */
  private roleHasPermission(roles: UserRole[], permission: Permission): boolean {
    const rolePermissions: Record<UserRole, Permission[]> = {
      [UserRole.GUEST]: [Permission.READ],
      [UserRole.USER]: [Permission.READ, Permission.WRITE],
      [UserRole.PREMIUM]: [Permission.READ, Permission.WRITE],
      [UserRole.MODERATOR]: [Permission.READ, Permission.WRITE, Permission.MODERATE],
      [UserRole.ADMIN]: [Permission.READ, Permission.WRITE, Permission.MODERATE, Permission.DELETE],
      [UserRole.SUPER_ADMIN]: [Permission.READ, Permission.WRITE, Permission.MODERATE, Permission.DELETE, Permission.ADMIN, Permission.SYSTEM]
    };

    return roles.some(role => rolePermissions[role]?.includes(permission));
  }
}
