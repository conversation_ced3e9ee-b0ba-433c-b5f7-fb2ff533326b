/**
 * 用户系统测试套件
 * 验证易经"变化适应"智慧的实现效果
 */

describe('用户系统测试 - 易经变化适应智慧', () => {
  // 模拟用户系统的核心功能
  const UserState = {
    INACTIVE: 'inactive',
    ACTIVE: 'active',
    SUSPENDED: 'suspended',
    ARCHIVED: 'archived',
    PENDING: 'pending',
  };

  const UserRole = {
    GUEST: 'guest',
    USER: 'user',
    MODERATOR: 'moderator',
    ADMIN: 'admin',
    SUPER_ADMIN: 'super_admin',
  };

  const AuthMethod = {
    EMAIL_PASSWORD: 'email_password',
    OAUTH_GOOGLE: 'oauth_google',
    OAUTH_GITHUB: 'oauth_github',
    API_KEY: 'api_key',
    JWT: 'jwt',
  };

  const Permission = {
    READ: 'read',
    WRITE: 'write',
    DELETE: 'delete',
    ADMIN: 'admin',
    MODERATE: 'moderate',
  };

  // 模拟用户系统类
  class MockUserSystem {
    constructor() {
      this.users = new Map();
      this.nextId = 1;
    }

    generateId() {
      return `user_${this.nextId++}`;
    }

    async createUser(request) {
      // 检查邮箱唯一性
      for (const user of this.users.values()) {
        if (user.profile.email === request.email) {
          throw new Error('Email already exists');
        }
        if (request.username && user.profile.username === request.username) {
          throw new Error('Username already exists');
        }
      }

      const userId = this.generateId();
      const now = new Date();

      const user = {
        profile: {
          id: userId,
          email: request.email,
          username: request.username,
          displayName: request.displayName || request.username || request.email.split('@')[0],
        },
        state: request.authMethod === AuthMethod.EMAIL_PASSWORD ? UserState.PENDING : UserState.ACTIVE,
        stateHistory: [{
          id: `transition_${Date.now()}`,
          userId,
          fromState: UserState.INACTIVE,
          toState: request.authMethod === AuthMethod.EMAIL_PASSWORD ? UserState.PENDING : UserState.ACTIVE,
          reason: 'User created',
          timestamp: now,
        }],
        auth: [{
          id: `auth_${Date.now()}`,
          userId,
          method: request.authMethod,
          identifier: request.email,
          isVerified: request.authMethod !== AuthMethod.EMAIL_PASSWORD,
          credentials: request.password ? `hashed_${request.password}` : undefined,
        }],
        permissions: {
          roles: [request.initialRole || UserRole.USER],
          permissions: [],
        },
        createdAt: now,
        updatedAt: now,
        metadata: request.metadata || {},
      };

      this.users.set(userId, user);
      return user;
    }

    async authenticate(identifier, credentials, method = AuthMethod.EMAIL_PASSWORD) {
      for (const user of this.users.values()) {
        const auth = user.auth.find(a => a.identifier === identifier && a.method === method);
        if (auth) {
          if (method === AuthMethod.EMAIL_PASSWORD) {
            if (auth.credentials === `hashed_${credentials}` && auth.isVerified) {
              return {
                success: true,
                user,
                token: `token_${Date.now()}`,
                expiresAt: new Date(Date.now() + 3600000),
              };
            } else {
              return { success: false, error: 'Invalid credentials' };
            }
          } else {
            return {
              success: true,
              user,
              token: `token_${Date.now()}`,
              expiresAt: new Date(Date.now() + 3600000),
            };
          }
        }
      }
      return { success: false, error: 'User not found' };
    }

    async getUserById(id) {
      return this.users.get(id) || null;
    }

    async changeUserState(userId, newState, reason, currentUser) {
      const user = this.users.get(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // 简化的权限检查
      if (currentUser && !this.hasPermission(currentUser, Permission.MODERATE)) {
        throw new Error('State transition not allowed: insufficient permissions');
      }

      const transition = {
        id: `transition_${Date.now()}`,
        userId,
        fromState: user.state,
        toState: newState,
        reason,
        triggeredBy: currentUser?.profile.id,
        timestamp: new Date(),
      };

      user.state = newState;
      user.updatedAt = new Date();
      user.stateHistory.push(transition);

      if (newState === UserState.SUSPENDED) {
        user.metadata.suspendedAt = new Date();
      } else if (newState === UserState.ARCHIVED) {
        user.metadata.archivedAt = new Date();
      }

      this.users.set(userId, user);
      return user;
    }

    hasPermission(user, permission) {
      const rolePermissions = {
        [UserRole.GUEST]: [Permission.READ],
        [UserRole.USER]: [Permission.READ, Permission.WRITE],
        [UserRole.MODERATOR]: [Permission.READ, Permission.WRITE, Permission.MODERATE],
        [UserRole.ADMIN]: [Permission.READ, Permission.WRITE, Permission.MODERATE, Permission.DELETE, Permission.ADMIN],
        [UserRole.SUPER_ADMIN]: [Permission.READ, Permission.WRITE, Permission.MODERATE, Permission.DELETE, Permission.ADMIN],
      };

      return user.permissions.roles.some(role =>
        rolePermissions[role]?.includes(permission)
      ) || user.permissions.permissions.includes(permission);
    }

    async grantPermission(userId, permission, currentUser) {
      const user = this.users.get(userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (currentUser && !this.hasPermission(currentUser, Permission.ADMIN)) {
        throw new Error('Permission denied');
      }

      if (!user.permissions.permissions.includes(permission)) {
        user.permissions.permissions.push(permission);
        user.updatedAt = new Date();
        this.users.set(userId, user);
      }
    }

    async revokePermission(userId, permission, currentUser) {
      const user = this.users.get(userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (currentUser && !this.hasPermission(currentUser, Permission.ADMIN)) {
        throw new Error('Permission denied');
      }

      const index = user.permissions.permissions.indexOf(permission);
      if (index > -1) {
        user.permissions.permissions.splice(index, 1);
        user.updatedAt = new Date();
        this.users.set(userId, user);
      }
    }
  }

  // 工具函数
  const UserSystemUtils = {
    validatePassword(password) {
      const errors = [];
      
      if (password.length < 8) {
        errors.push('Password must be at least 8 characters long');
      }
      if (!/[A-Z]/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
      }
      if (!/[a-z]/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
      }
      if (!/\d/.test(password)) {
        errors.push('Password must contain at least one number');
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },

    isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    },

    isValidUsername(username) {
      const usernameRegex = /^[a-zA-Z0-9_-]{3,20}$/;
      return usernameRegex.test(username);
    },
  };

  let userSystem;

  beforeEach(() => {
    userSystem = new MockUserSystem();
  });

  describe('用户创建 - 体现"自强不息"', () => {
    test('应该能够创建基础用户', async () => {
      const request = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        username: 'testuser',
        displayName: 'Test User',
        authMethod: AuthMethod.EMAIL_PASSWORD,
      };

      const user = await userSystem.createUser(request);

      expect(user.profile.email).toBe(request.email);
      expect(user.profile.username).toBe(request.username);
      expect(user.profile.displayName).toBe(request.displayName);
      expect(user.state).toBe(UserState.PENDING);
      expect(user.permissions.roles).toContain(UserRole.USER);
      expect(user.auth).toHaveLength(1);
      expect(user.auth[0].method).toBe(AuthMethod.EMAIL_PASSWORD);
      expect(user.stateHistory).toHaveLength(1);
    });

    test('应该能够创建OAuth用户', async () => {
      const request = {
        email: '<EMAIL>',
        username: 'oauthuser',
        authMethod: AuthMethod.OAUTH_GOOGLE,
        initialRole: UserRole.USER,
      };

      const user = await userSystem.createUser(request);

      expect(user.state).toBe(UserState.ACTIVE);
      expect(user.auth[0].method).toBe(AuthMethod.OAUTH_GOOGLE);
      expect(user.auth[0].isVerified).toBe(true);
    });

    test('应该拒绝重复邮箱', async () => {
      const request = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        authMethod: AuthMethod.EMAIL_PASSWORD,
      };

      await userSystem.createUser(request);

      await expect(userSystem.createUser(request)).rejects.toThrow('Email already exists');
    });
  });

  describe('用户认证 - 体现"适应性"', () => {
    let testUser;

    beforeEach(async () => {
      const request = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        username: 'authuser',
        authMethod: AuthMethod.EMAIL_PASSWORD,
      };

      testUser = await userSystem.createUser(request);
      
      // 激活用户并设置为已验证
      await userSystem.changeUserState(testUser.profile.id, UserState.ACTIVE);
      testUser.auth[0].isVerified = true;
      userSystem.users.set(testUser.profile.id, testUser);
    });

    test('应该能够使用邮箱密码认证', async () => {
      const result = await userSystem.authenticate(
        '<EMAIL>',
        'SecurePass123!',
        AuthMethod.EMAIL_PASSWORD
      );

      expect(result.success).toBe(true);
      expect(result.user).toBeDefined();
      expect(result.token).toBeDefined();
      expect(result.expiresAt).toBeDefined();
    });

    test('应该拒绝错误密码', async () => {
      const result = await userSystem.authenticate(
        '<EMAIL>',
        'WrongPassword',
        AuthMethod.EMAIL_PASSWORD
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('用户状态管理 - 体现"穷则变，变则通"', () => {
    let testUser;
    let adminUser;

    beforeEach(async () => {
      const userRequest = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        authMethod: AuthMethod.EMAIL_PASSWORD,
      };
      testUser = await userSystem.createUser(userRequest);

      const adminRequest = {
        email: '<EMAIL>',
        password: 'AdminPass123!',
        authMethod: AuthMethod.EMAIL_PASSWORD,
        initialRole: UserRole.ADMIN,
      };
      adminUser = await userSystem.createUser(adminRequest);
      await userSystem.changeUserState(adminUser.profile.id, UserState.ACTIVE);
    });

    test('应该能够激活待审核用户', async () => {
      expect(testUser.state).toBe(UserState.PENDING);

      const updatedUser = await userSystem.changeUserState(
        testUser.profile.id,
        UserState.ACTIVE,
        'Manual activation',
        adminUser
      );

      expect(updatedUser.state).toBe(UserState.ACTIVE);
      expect(updatedUser.stateHistory).toHaveLength(2);
      expect(updatedUser.stateHistory[1].fromState).toBe(UserState.PENDING);
      expect(updatedUser.stateHistory[1].toState).toBe(UserState.ACTIVE);
      expect(updatedUser.stateHistory[1].reason).toBe('Manual activation');
    });

    test('应该能够暂停活跃用户', async () => {
      await userSystem.changeUserState(testUser.profile.id, UserState.ACTIVE);
      
      const updatedUser = await userSystem.changeUserState(
        testUser.profile.id,
        UserState.SUSPENDED,
        'Policy violation',
        adminUser
      );

      expect(updatedUser.state).toBe(UserState.SUSPENDED);
      expect(updatedUser.metadata.suspendedAt).toBeDefined();
    });
  });

  describe('权限管理 - 体现"平衡性"', () => {
    let testUser;
    let adminUser;

    beforeEach(async () => {
      const userRequest = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        authMethod: AuthMethod.EMAIL_PASSWORD,
      };
      testUser = await userSystem.createUser(userRequest);

      const adminRequest = {
        email: '<EMAIL>',
        password: 'AdminPass123!',
        authMethod: AuthMethod.EMAIL_PASSWORD,
        initialRole: UserRole.ADMIN,
      };
      adminUser = await userSystem.createUser(adminRequest);
      // 激活管理员用户
      await userSystem.changeUserState(adminUser.profile.id, UserState.ACTIVE);
    });

    test('应该能够授予权限', async () => {
      await userSystem.grantPermission(
        testUser.profile.id,
        Permission.MODERATE,
        adminUser
      );

      const updatedUser = await userSystem.getUserById(testUser.profile.id);
      expect(updatedUser.permissions.permissions).toContain(Permission.MODERATE);
    });

    test('应该能够撤销权限', async () => {
      await userSystem.grantPermission(
        testUser.profile.id,
        Permission.MODERATE,
        adminUser
      );

      await userSystem.revokePermission(
        testUser.profile.id,
        Permission.MODERATE,
        adminUser
      );

      const updatedUser = await userSystem.getUserById(testUser.profile.id);
      expect(updatedUser.permissions.permissions).not.toContain(Permission.MODERATE);
    });
  });

  describe('系统工具函数', () => {
    test('应该能够验证密码强度', () => {
      const weakPassword = UserSystemUtils.validatePassword('123');
      expect(weakPassword.isValid).toBe(false);
      expect(weakPassword.errors.length).toBeGreaterThan(0);

      const strongPassword = UserSystemUtils.validatePassword('SecurePass123!');
      expect(strongPassword.isValid).toBe(true);
      expect(strongPassword.errors.length).toBe(0);
    });

    test('应该能够验证邮箱格式', () => {
      expect(UserSystemUtils.isValidEmail('<EMAIL>')).toBe(true);
      expect(UserSystemUtils.isValidEmail('invalid-email')).toBe(false);
      expect(UserSystemUtils.isValidEmail('test@')).toBe(false);
    });

    test('应该能够验证用户名格式', () => {
      expect(UserSystemUtils.isValidUsername('validuser123')).toBe(true);
      expect(UserSystemUtils.isValidUsername('valid_user')).toBe(true);
      expect(UserSystemUtils.isValidUsername('valid-user')).toBe(true);
      expect(UserSystemUtils.isValidUsername('ab')).toBe(false);
      expect(UserSystemUtils.isValidUsername('user with spaces')).toBe(false);
      expect(UserSystemUtils.isValidUsername('user@domain')).toBe(false);
    });
  });
});
