/**
 * 用户数据持久化层
 * 体现易经"通则久"的持续性，提供灵活的数据存储抽象
 */

import {
  User,
  UserAuth,
  UserActivity,
  UserStateTransition,
  UserFilter,
  UserQueryOptions,
} from './types';

// 数据库连接配置
export interface DatabaseConfig {
  type: 'memory' | 'mongodb' | 'postgresql' | 'mysql' | 'sqlite';
  connectionString?: string;
  options?: Record<string, any>;
}

// 查询结果
export interface QueryResult<T> {
  data: T[];
  total: number;
  hasMore: boolean;
  nextCursor?: string;
}

// 用户仓储接口
export interface IUserRepository {
  // 基础CRUD操作
  create(user: User): Promise<User>;
  findById(id: string): Promise<User | null>;
  findByEmail(email: string): Promise<User | null>;
  findByUsername(username: string): Promise<User | null>;
  update(id: string, updates: Partial<User>): Promise<User>;
  delete(id: string): Promise<void>;

  // 查询操作
  query(filter?: UserFilter, options?: UserQueryOptions): Promise<QueryResult<User>>;
  count(filter?: UserFilter): Promise<number>;
  exists(id: string): Promise<boolean>;

  // 认证相关
  findByAuthIdentifier(identifier: string, method: string): Promise<User | null>;
  updateAuth(userId: string, auth: UserAuth): Promise<void>;
  removeAuth(userId: string, authId: string): Promise<void>;

  // 活动记录
  addActivity(userId: string, activity: UserActivity): Promise<void>;
  getActivities(userId: string, limit?: number, offset?: number): Promise<UserActivity[]>;

  // 状态转换记录
  addStateTransition(userId: string, transition: UserStateTransition): Promise<void>;
  getStateHistory(userId: string): Promise<UserStateTransition[]>;

  // 批量操作
  bulkCreate(users: User[]): Promise<User[]>;
  bulkUpdate(updates: Array<{ id: string; data: Partial<User> }>): Promise<void>;
  bulkDelete(ids: string[]): Promise<void>;
}

// 内存存储实现（用于开发和测试）
export class MemoryUserRepository implements IUserRepository {
  private users: Map<string, User> = new Map();
  private activities: Map<string, UserActivity[]> = new Map();
  private stateHistory: Map<string, UserStateTransition[]> = new Map();

  async create(user: User): Promise<User> {
    this.users.set(user.profile.id, { ...user });
    return user;
  }

  async findById(id: string): Promise<User | null> {
    const user = this.users.get(id);
    return user ? { ...user } : null;
  }

  async findByEmail(email: string): Promise<User | null> {
    for (const user of this.users.values()) {
      if (user.profile.email === email) {
        return { ...user };
      }
    }
    return null;
  }

  async findByUsername(username: string): Promise<User | null> {
    for (const user of this.users.values()) {
      if (user.profile.username === username) {
        return { ...user };
      }
    }
    return null;
  }

  async update(id: string, updates: Partial<User>): Promise<User> {
    const user = this.users.get(id);
    if (!user) {
      throw new Error('User not found');
    }

    const updatedUser = { ...user, ...updates, updatedAt: new Date() };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async delete(id: string): Promise<void> {
    this.users.delete(id);
    this.activities.delete(id);
    this.stateHistory.delete(id);
  }

  async query(filter?: UserFilter, options?: UserQueryOptions): Promise<QueryResult<User>> {
    let users = Array.from(this.users.values());

    // 应用过滤条件
    if (filter) {
      users = this.applyFilter(users, filter);
    }

    const total = users.length;

    // 应用排序和分页
    if (options) {
      users = this.applySortingAndPaging(users, options);
    }

    return {
      data: users.map(user => ({ ...user })),
      total,
      hasMore: options?.offset ? (options.offset + users.length) < total : false,
    };
  }

  async count(filter?: UserFilter): Promise<number> {
    let users = Array.from(this.users.values());
    
    if (filter) {
      users = this.applyFilter(users, filter);
    }

    return users.length;
  }

  async exists(id: string): Promise<boolean> {
    return this.users.has(id);
  }

  async findByAuthIdentifier(identifier: string, method: string): Promise<User | null> {
    for (const user of this.users.values()) {
      const auth = user.auth.find(a => a.identifier === identifier && a.method === method);
      if (auth) {
        return { ...user };
      }
    }
    return null;
  }

  async updateAuth(userId: string, auth: UserAuth): Promise<void> {
    const user = this.users.get(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const authIndex = user.auth.findIndex(a => a.id === auth.id);
    if (authIndex >= 0) {
      user.auth[authIndex] = auth;
    } else {
      user.auth.push(auth);
    }

    user.updatedAt = new Date();
    this.users.set(userId, user);
  }

  async removeAuth(userId: string, authId: string): Promise<void> {
    const user = this.users.get(userId);
    if (!user) {
      throw new Error('User not found');
    }

    user.auth = user.auth.filter(a => a.id !== authId);
    user.updatedAt = new Date();
    this.users.set(userId, user);
  }

  async addActivity(userId: string, activity: UserActivity): Promise<void> {
    const activities = this.activities.get(userId) || [];
    activities.push(activity);
    
    // 限制活动记录数量
    if (activities.length > 1000) {
      activities.splice(0, activities.length - 1000);
    }
    
    this.activities.set(userId, activities);
  }

  async getActivities(userId: string, limit: number = 50, offset: number = 0): Promise<UserActivity[]> {
    const activities = this.activities.get(userId) || [];
    return activities
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(offset, offset + limit);
  }

  async addStateTransition(userId: string, transition: UserStateTransition): Promise<void> {
    const history = this.stateHistory.get(userId) || [];
    history.push(transition);
    this.stateHistory.set(userId, history);
  }

  async getStateHistory(userId: string): Promise<UserStateTransition[]> {
    return this.stateHistory.get(userId) || [];
  }

  async bulkCreate(users: User[]): Promise<User[]> {
    const created: User[] = [];
    for (const user of users) {
      this.users.set(user.profile.id, { ...user });
      created.push(user);
    }
    return created;
  }

  async bulkUpdate(updates: Array<{ id: string; data: Partial<User> }>): Promise<void> {
    for (const update of updates) {
      const user = this.users.get(update.id);
      if (user) {
        const updatedUser = { ...user, ...update.data, updatedAt: new Date() };
        this.users.set(update.id, updatedUser);
      }
    }
  }

  async bulkDelete(ids: string[]): Promise<void> {
    for (const id of ids) {
      this.users.delete(id);
      this.activities.delete(id);
      this.stateHistory.delete(id);
    }
  }

  // 私有辅助方法
  private applyFilter(users: User[], filter: UserFilter): User[] {
    return users.filter(user => {
      if (filter.state && !filter.state.includes(user.state)) return false;
      if (filter.roles && !filter.roles.some(role => user.permissions.roles.includes(role))) return false;
      if (filter.createdAfter && user.createdAt < filter.createdAfter) return false;
      if (filter.createdBefore && user.createdAt > filter.createdBefore) return false;
      if (filter.lastActiveAfter && (!user.lastActiveAt || user.lastActiveAt < filter.lastActiveAfter)) return false;
      if (filter.lastActiveBefore && (!user.lastActiveAt || user.lastActiveAt > filter.lastActiveBefore)) return false;
      if (filter.tags && (!user.tags || !filter.tags.some(tag => user.tags!.includes(tag)))) return false;
      
      return true;
    });
  }

  private applySortingAndPaging(users: User[], options: UserQueryOptions): User[] {
    // 排序
    if (options.sortBy) {
      users.sort((a, b) => {
        const aValue = this.getNestedValue(a, options.sortBy!);
        const bValue = this.getNestedValue(b, options.sortBy!);
        
        if (options.sortOrder === 'desc') {
          return bValue > aValue ? 1 : -1;
        }
        return aValue > bValue ? 1 : -1;
      });
    }

    // 分页
    const offset = options.offset || 0;
    const limit = options.limit || 50;
    
    return users.slice(offset, offset + limit);
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
}

// 仓储工厂
export class RepositoryFactory {
  static createUserRepository(config: DatabaseConfig): IUserRepository {
    switch (config.type) {
      case 'memory':
        return new MemoryUserRepository();
      case 'mongodb':
        // return new MongoUserRepository(config);
        throw new Error('MongoDB repository not implemented yet');
      case 'postgresql':
        // return new PostgreSQLUserRepository(config);
        throw new Error('PostgreSQL repository not implemented yet');
      case 'mysql':
        // return new MySQLUserRepository(config);
        throw new Error('MySQL repository not implemented yet');
      case 'sqlite':
        // return new SQLiteUserRepository(config);
        throw new Error('SQLite repository not implemented yet');
      default:
        throw new Error(`Unsupported database type: ${config.type}`);
    }
  }
}

// 数据库迁移接口
export interface IMigration {
  version: string;
  description: string;
  up(): Promise<void>;
  down(): Promise<void>;
}

// 迁移管理器
export class MigrationManager {
  private migrations: IMigration[] = [];
  private currentVersion: string = '0.0.0';

  addMigration(migration: IMigration): void {
    this.migrations.push(migration);
    this.migrations.sort((a, b) => a.version.localeCompare(b.version));
  }

  async migrate(targetVersion?: string): Promise<void> {
    const target = targetVersion || this.getLatestVersion();
    
    for (const migration of this.migrations) {
      if (migration.version > this.currentVersion && migration.version <= target) {
        console.log(`Running migration ${migration.version}: ${migration.description}`);
        await migration.up();
        this.currentVersion = migration.version;
      }
    }
  }

  async rollback(targetVersion: string): Promise<void> {
    const reversedMigrations = [...this.migrations].reverse();
    
    for (const migration of reversedMigrations) {
      if (migration.version <= this.currentVersion && migration.version > targetVersion) {
        console.log(`Rolling back migration ${migration.version}: ${migration.description}`);
        await migration.down();
        this.currentVersion = this.getPreviousVersion(migration.version);
      }
    }
  }

  private getLatestVersion(): string {
    return this.migrations.length > 0 
      ? this.migrations[this.migrations.length - 1].version 
      : '0.0.0';
  }

  private getPreviousVersion(version: string): string {
    const index = this.migrations.findIndex(m => m.version === version);
    return index > 0 ? this.migrations[index - 1].version : '0.0.0';
  }
}
