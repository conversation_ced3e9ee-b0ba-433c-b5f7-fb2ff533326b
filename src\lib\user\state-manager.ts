/**
 * 用户状态管理器
 * 基于易经"穷则变，变则通，通则久"的智慧
 * 实现用户状态的动态转换和生命周期管理
 */

import {
  User,
  UserState,
  UserRole,
  Permission,
  StateTransitionRule,
  UserStateTransition,
  UserSystemError,
  UserErrorCodes,
} from './types';

export class UserStateManager {
  private transitionRules: Map<string, StateTransitionRule[]> = new Map();
  private transitionHistory: UserStateTransition[] = [];

  constructor(rules: StateTransitionRule[] = []) {
    this.initializeDefaultRules();
    this.addTransitionRules(rules);
  }

  /**
   * 初始化默认状态转换规则
   * 体现易经八卦变化规律
   */
  private initializeDefaultRules(): void {
    const defaultRules: StateTransitionRule[] = [
      // 潜龙勿用 -> 见龙在田 (激活用户)
      {
        from: UserState.INACTIVE,
        to: UserState.ACTIVE,
        validator: (user: User) => this.hasValidAuth(user),
        onTransition: async (user: User) => {
          user.lastActiveAt = new Date();
        },
      },

      // 见龙在田 -> 亢龙有悔 (暂停用户)
      {
        from: UserState.ACTIVE,
        to: UserState.SUSPENDED,
        requiredPermission: Permission.MODERATE,
        onTransition: async (user: User) => {
          // 记录暂停原因
          user.metadata = { ...user.metadata, suspendedAt: new Date() };
        },
      },

      // 亢龙有悔 -> 见龙在田 (恢复用户)
      {
        from: UserState.SUSPENDED,
        to: UserState.ACTIVE,
        requiredPermission: Permission.MODERATE,
        validator: (user: User) => {
          // 检查暂停时间是否足够
          const suspendedAt = user.metadata?.suspendedAt;
          if (!suspendedAt) return true;
          
          const daysSuspended = (Date.now() - new Date(suspendedAt).getTime()) / (1000 * 60 * 60 * 24);
          return daysSuspended >= 1; // 至少暂停1天
        },
      },

      // 任何状态 -> 群龙无首 (归档用户)
      {
        from: UserState.ACTIVE,
        to: UserState.ARCHIVED,
        requiredPermission: Permission.ADMIN,
        onTransition: async (user: User) => {
          user.metadata = { ...user.metadata, archivedAt: new Date() };
        },
      },

      {
        from: UserState.SUSPENDED,
        to: UserState.ARCHIVED,
        requiredPermission: Permission.ADMIN,
      },

      // 飞龙在天 -> 见龙在田 (审核通过)
      {
        from: UserState.PENDING,
        to: UserState.ACTIVE,
        requiredPermission: Permission.MODERATE,
        validator: (user: User) => this.hasValidAuth(user),
      },

      // 飞龙在天 -> 亢龙有悔 (审核拒绝)
      {
        from: UserState.PENDING,
        to: UserState.SUSPENDED,
        requiredPermission: Permission.MODERATE,
      },
    ];

    this.addTransitionRules(defaultRules);
  }

  /**
   * 添加状态转换规则
   */
  public addTransitionRules(rules: StateTransitionRule[]): void {
    for (const rule of rules) {
      const key = `${rule.from}->${rule.to}`;
      const existingRules = this.transitionRules.get(key) || [];
      existingRules.push(rule);
      this.transitionRules.set(key, existingRules);
    }
  }

  /**
   * 执行状态转换
   * 体现"变则通"的核心理念
   */
  public async transitionState(
    user: User,
    toState: UserState,
    context?: {
      triggeredBy?: string;
      reason?: string;
      metadata?: Record<string, any>;
      currentUserRole?: UserRole;
      currentUserPermissions?: Permission[];
    }
  ): Promise<UserStateTransition> {
    const fromState = user.state;
    
    // 检查是否为有效转换
    if (fromState === toState) {
      throw new UserSystemError(
        `User is already in state: ${toState}`,
        UserErrorCodes.INVALID_STATE_TRANSITION
      );
    }

    // 获取转换规则
    const rules = this.getTransitionRules(fromState, toState);
    if (rules.length === 0) {
      throw new UserSystemError(
        `Invalid state transition from ${fromState} to ${toState}`,
        UserErrorCodes.INVALID_STATE_TRANSITION
      );
    }

    // 验证转换条件
    const applicableRule = await this.findApplicableRule(rules, user, context);
    if (!applicableRule) {
      throw new UserSystemError(
        `State transition not allowed: insufficient permissions or failed validation`,
        UserErrorCodes.PERMISSION_DENIED
      );
    }

    // 执行转换前的钩子
    if (applicableRule.onTransition) {
      await applicableRule.onTransition(user, context);
    }

    // 创建状态转换记录
    const transition: UserStateTransition = {
      id: this.generateTransitionId(),
      userId: user.profile.id,
      fromState,
      toState,
      reason: context?.reason,
      triggeredBy: context?.triggeredBy,
      timestamp: new Date(),
      metadata: context?.metadata,
    };

    // 更新用户状态
    user.state = toState;
    user.updatedAt = new Date();
    user.stateHistory.push(transition);

    // 记录到历史
    this.transitionHistory.push(transition);

    return transition;
  }

  /**
   * 获取可能的状态转换
   */
  public getPossibleTransitions(
    fromState: UserState,
    userRole?: UserRole,
    userPermissions?: Permission[]
  ): UserState[] {
    const possibleStates: UserState[] = [];

    for (const [key, rules] of this.transitionRules.entries()) {
      const [from] = key.split('->');
      if (from === fromState) {
        for (const rule of rules) {
          if (this.checkRulePermissions(rule, userRole, userPermissions)) {
            possibleStates.push(rule.to);
          }
        }
      }
    }

    return [...new Set(possibleStates)];
  }

  /**
   * 获取状态转换历史
   */
  public getTransitionHistory(userId?: string): UserStateTransition[] {
    if (userId) {
      return this.transitionHistory.filter(t => t.userId === userId);
    }
    return [...this.transitionHistory];
  }

  /**
   * 检查状态转换是否有效
   */
  public canTransition(
    fromState: UserState,
    toState: UserState,
    userRole?: UserRole,
    userPermissions?: Permission[]
  ): boolean {
    const rules = this.getTransitionRules(fromState, toState);
    return rules.some(rule => this.checkRulePermissions(rule, userRole, userPermissions));
  }

  /**
   * 获取状态统计信息
   */
  public getStateStatistics(): Record<UserState, number> {
    const stats: Record<UserState, number> = {
      [UserState.INACTIVE]: 0,
      [UserState.ACTIVE]: 0,
      [UserState.SUSPENDED]: 0,
      [UserState.ARCHIVED]: 0,
      [UserState.PENDING]: 0,
    };

    // 这里应该从数据库获取实际统计
    // 暂时返回空统计
    return stats;
  }

  // 私有辅助方法

  private getTransitionRules(from: UserState, to: UserState): StateTransitionRule[] {
    const key = `${from}->${to}`;
    return this.transitionRules.get(key) || [];
  }

  private async findApplicableRule(
    rules: StateTransitionRule[],
    user: User,
    context?: any
  ): Promise<StateTransitionRule | null> {
    for (const rule of rules) {
      // 检查权限要求
      if (!this.checkRulePermissions(
        rule,
        context?.currentUserRole,
        context?.currentUserPermissions
      )) {
        continue;
      }

      // 检查自定义验证器
      if (rule.validator && !rule.validator(user, context)) {
        continue;
      }

      return rule;
    }

    return null;
  }

  private checkRulePermissions(
    rule: StateTransitionRule,
    userRole?: UserRole,
    userPermissions?: Permission[]
  ): boolean {
    // 检查角色要求
    if (rule.requiredRole && userRole !== rule.requiredRole) {
      // 检查是否有更高级别的角色
      const roleHierarchy = [
        UserRole.GUEST,
        UserRole.USER,
        UserRole.MODERATOR,
        UserRole.ADMIN,
        UserRole.SUPER_ADMIN,
      ];
      
      const requiredIndex = roleHierarchy.indexOf(rule.requiredRole);
      const userIndex = roleHierarchy.indexOf(userRole || UserRole.GUEST);
      
      if (userIndex < requiredIndex) {
        return false;
      }
    }

    // 检查权限要求
    if (rule.requiredPermission && userPermissions) {
      if (!userPermissions.includes(rule.requiredPermission)) {
        return false;
      }
    }

    return true;
  }

  private hasValidAuth(user: User): boolean {
    return user.auth.some(auth => auth.isVerified);
  }

  private generateTransitionId(): string {
    return `transition_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
