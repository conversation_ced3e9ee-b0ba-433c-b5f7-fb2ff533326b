/**
 * 用户服务层
 * 整合用户管理的所有功能，体现易经"通则久"的持续性
 */

import {
  User,
  UserProfile,
  UserState,
  UserRole,
  Permission,
  AuthMethod,
  CreateUserRequest,
  UpdateUserRequest,
  UserQueryOptions,
  UserFilter,
  UserSystemError,
  UserErrorCodes,
  UserActivity,
  UserPreferences,
} from './types';
import { UserStateManager } from './state-manager';
import { AuthManager, AuthConfig, AuthResult } from './auth-manager';

export interface UserServiceConfig {
  auth: AuthConfig;
  defaultRole: UserRole;
  defaultState: UserState;
  enableActivityLogging: boolean;
  maxUsersPerQuery: number;
}

export class UserService {
  private stateManager: UserStateManager;
  private authManager: AuthManager;
  private config: UserServiceConfig;
  private users: Map<string, User> = new Map(); // 模拟数据存储

  constructor(config: UserServiceConfig) {
    this.config = config;
    this.stateManager = new UserStateManager();
    this.authManager = new AuthManager(config.auth);
  }

  /**
   * 创建新用户
   * 体现"自强不息"的创新精神
   */
  public async createUser(request: CreateUserRequest): Promise<User> {
    // 验证邮箱唯一性
    const existingUser = await this.findUserByEmail(request.email);
    if (existingUser) {
      throw new UserSystemError(
        'Email already exists',
        UserErrorCodes.EMAIL_ALREADY_EXISTS
      );
    }

    // 验证用户名唯一性
    if (request.username) {
      const existingUsername = await this.findUserByUsername(request.username);
      if (existingUsername) {
        throw new UserSystemError(
          'Username already exists',
          UserErrorCodes.USERNAME_ALREADY_EXISTS
        );
      }
    }

    // 创建用户实体
    const userId = this.generateUserId();
    const now = new Date();

    const user: User = {
      profile: {
        id: userId,
        email: request.email,
        username: request.username,
        displayName: request.displayName || request.username || request.email.split('@')[0],
      },
      state: request.authMethod === AuthMethod.EMAIL_PASSWORD ? UserState.PENDING : this.config.defaultState,
      stateHistory: [],
      auth: [],
      permissions: {
        roles: [request.initialRole || this.config.defaultRole],
        permissions: [],
      },
      createdAt: now,
      updatedAt: now,
      metadata: request.metadata || {},
    };

    // 创建认证信息
    const authId = this.generateAuthId();
    const userAuth = {
      id: authId,
      userId,
      method: request.authMethod,
      identifier: request.email,
      isVerified: request.authMethod !== AuthMethod.EMAIL_PASSWORD,
      metadata: {},
      credentials: ``
    };

    // 处理密码
    if (request.password && request.authMethod === AuthMethod.EMAIL_PASSWORD) {
      userAuth.credentials = await this.hashPassword(request.password);
    }

    user.auth.push(userAuth);

    // 记录初始状态转换
    const initialTransition = {
      id: this.generateTransitionId(),
      userId,
      fromState: UserState.INACTIVE,
      toState: user.state,
      reason: 'User created',
      timestamp: now,
    };
    user.stateHistory.push(initialTransition);

    // 保存用户
    await this.saveUser(user);

    // 记录活动
    if (this.config.enableActivityLogging) {
      await this.logActivity(user, 'user_created', 'user', { authMethod: request.authMethod });
    }

    return user;
  }

  /**
   * 用户认证
   */
  public async authenticate(
    identifier: string,
    credentials: string,
    method: AuthMethod = AuthMethod.EMAIL_PASSWORD
  ): Promise<AuthResult> {
    const result = await this.authManager.authenticate(identifier, credentials, method);
    
    if (result.success && result.user) {
      // 记录登录活动
      if (this.config.enableActivityLogging) {
        await this.logActivity(result.user, 'user_login', 'auth', { method });
      }
    }

    return result;
  }

  /**
   * 获取用户信息
   */
  public async getUserById(id: string, options?: UserQueryOptions): Promise<User | null> {
    const user = this.users.get(id);
    if (!user) return null;

    return this.filterUserData(user, options);
  }

  /**
   * 根据邮箱获取用户
   */
  public async getUserByEmail(email: string, options?: UserQueryOptions): Promise<User | null> {
    const user = await this.findUserByEmail(email);
    if (!user) return null;

    return this.filterUserData(user, options);
  }

  /**
   * 更新用户信息
   */
  public async updateUser(
    userId: string,
    updates: UpdateUserRequest,
    currentUser?: User
  ): Promise<User> {
    const user = await this.getUserById(userId);
    if (!user) {
      throw new UserSystemError('User not found', UserErrorCodes.USER_NOT_FOUND);
    }

    // 权限检查
    if (currentUser && currentUser.profile.id !== userId) {
      if (!this.authManager.hasPermission(currentUser, Permission.MODERATE)) {
        throw new UserSystemError('Permission denied', UserErrorCodes.PERMISSION_DENIED);
      }
    }

    // 更新用户资料
    if (updates.profile) {
      user.profile = { ...user.profile, ...updates.profile };
    }

    // 更新偏好设置
    if (updates.preferences) {
      user.preferences = { ...user.preferences, ...updates.preferences };
    }

    // 更新元数据
    if (updates.metadata) {
      user.metadata = { ...user.metadata, ...updates.metadata };
    }

    user.updatedAt = new Date();

    await this.saveUser(user);

    // 记录活动
    if (this.config.enableActivityLogging) {
      await this.logActivity(user, 'user_updated', 'user', { 
        updatedBy: currentUser?.profile.id,
        fields: Object.keys(updates)
      });
    }

    return user;
  }

  /**
   * 更改用户状态
   */
  public async changeUserState(
    userId: string,
    newState: UserState,
    reason?: string,
    currentUser?: User
  ): Promise<User> {
    const user = await this.getUserById(userId);
    if (!user) {
      throw new UserSystemError('User not found', UserErrorCodes.USER_NOT_FOUND);
    }

    // 执行状态转换
    await this.stateManager.transitionState(user, newState, {
      triggeredBy: currentUser?.profile.id,
      reason,
      currentUserRole: currentUser?.permissions.roles[0],
      currentUserPermissions: currentUser?.permissions.permissions,
    });

    await this.saveUser(user);

    // 记录活动
    if (this.config.enableActivityLogging) {
      await this.logActivity(user, 'state_changed', 'user', {
        newState,
        reason,
        changedBy: currentUser?.profile.id,
      });
    }

    return user;
  }

  /**
   * 查询用户列表
   */
  public async queryUsers(
    filter?: UserFilter,
    options?: UserQueryOptions
  ): Promise<{ users: User[]; total: number }> {
    let users = Array.from(this.users.values());

    // 应用过滤条件
    if (filter) {
      users = this.applyFilter(users, filter);
    }

    const total = users.length;

    // 应用分页和排序
    if (options) {
      users = this.applySortingAndPaging(users, options);
    }

    // 过滤返回数据
    const filteredUsers = users.map(user => this.filterUserData(user, options));

    return { users: filteredUsers, total };
  }

  /**
   * 删除用户
   */
  public async deleteUser(userId: string, currentUser?: User): Promise<void> {
    const user = await this.getUserById(userId);
    if (!user) {
      throw new UserSystemError('User not found', UserErrorCodes.USER_NOT_FOUND);
    }

    // 权限检查
    if (currentUser && !this.authManager.hasPermission(currentUser, Permission.ADMIN)) {
      throw new UserSystemError('Permission denied', UserErrorCodes.PERMISSION_DENIED);
    }

    // 软删除：转换到归档状态
    await this.changeUserState(userId, UserState.ARCHIVED, 'User deleted', currentUser);

    // 记录活动
    if (this.config.enableActivityLogging) {
      await this.logActivity(user, 'user_deleted', 'user', {
        deletedBy: currentUser?.profile.id,
      });
    }
  }

  /**
   * 获取用户活动记录
   */
  public async getUserActivities(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<UserActivity[]> {
    const user = await this.getUserById(userId);
    if (!user || !user.activities) return [];

    return user.activities
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(offset, offset + limit);
  }

  /**
   * 权限管理方法
   */
  public async grantPermission(
    userId: string,
    permission: Permission,
    currentUser?: User,
    expiresAt?: Date
  ): Promise<void> {
    const user = await this.getUserById(userId);
    if (!user) {
      throw new UserSystemError('User not found', UserErrorCodes.USER_NOT_FOUND);
    }

    if (currentUser && !this.authManager.hasPermission(currentUser, Permission.ADMIN)) {
      throw new UserSystemError('Permission denied', UserErrorCodes.PERMISSION_DENIED);
    }

    await this.authManager.grantPermission(user, permission, expiresAt);
    await this.saveUser(user);

    if (this.config.enableActivityLogging) {
      await this.logActivity(user, 'permission_granted', 'permission', {
        permission,
        grantedBy: currentUser?.profile.id,
        expiresAt,
      });
    }
  }

  public async revokePermission(
    userId: string,
    permission: Permission,
    currentUser?: User
  ): Promise<void> {
    const user = await this.getUserById(userId);
    if (!user) {
      throw new UserSystemError('User not found', UserErrorCodes.USER_NOT_FOUND);
    }

    if (currentUser && !this.authManager.hasPermission(currentUser, Permission.ADMIN)) {
      throw new UserSystemError('Permission denied', UserErrorCodes.PERMISSION_DENIED);
    }

    await this.authManager.revokePermission(user, permission);
    await this.saveUser(user);

    if (this.config.enableActivityLogging) {
      await this.logActivity(user, 'permission_revoked', 'permission', {
        permission,
        revokedBy: currentUser?.profile.id,
      });
    }
  }

  // 私有辅助方法

  private async saveUser(user: User): Promise<void> {
    this.users.set(user.profile.id, user);
  }

  private async findUserByEmail(email: string): Promise<User | null> {
    for (const user of this.users.values()) {
      if (user.profile.email === email) {
        return user;
      }
    }
    return null;
  }

  private async findUserByUsername(username: string): Promise<User | null> {
    for (const user of this.users.values()) {
      if (user.profile.username === username) {
        return user;
      }
    }
    return null;
  }

  private filterUserData(user: User, options?: UserQueryOptions): User {
    const filtered = { ...user };

    if (!options?.includeAuth) {
      delete (filtered as any).auth;
    }

    if (!options?.includeActivities) {
      delete (filtered as any).activities;
    }

    if (!options?.includeStateHistory) {
      delete (filtered as any).stateHistory;
    }

    return filtered;
  }

  private applyFilter(users: User[], filter: UserFilter): User[] {
    return users.filter(user => {
      if (filter.state && !filter.state.includes(user.state)) return false;
      if (filter.roles && !filter.roles.some(role => user.permissions.roles.includes(role))) return false;
      if (filter.createdAfter && user.createdAt < filter.createdAfter) return false;
      if (filter.createdBefore && user.createdAt > filter.createdBefore) return false;
      if (filter.lastActiveAfter && (!user.lastActiveAt || user.lastActiveAt < filter.lastActiveAfter)) return false;
      if (filter.lastActiveBefore && (!user.lastActiveAt || user.lastActiveAt > filter.lastActiveBefore)) return false;
      if (filter.tags && (!user.tags || !filter.tags.some(tag => user.tags!.includes(tag)))) return false;
      
      return true;
    });
  }

  private applySortingAndPaging(users: User[], options: UserQueryOptions): User[] {
    // 排序
    if (options.sortBy) {
      users.sort((a, b) => {
        const aValue = this.getNestedValue(a, options.sortBy!);
        const bValue = this.getNestedValue(b, options.sortBy!);
        
        if (options.sortOrder === 'desc') {
          return bValue > aValue ? 1 : -1;
        }
        return aValue > bValue ? 1 : -1;
      });
    }

    // 分页
    const offset = options.offset || 0;
    const limit = Math.min(options.limit || 50, this.config.maxUsersPerQuery);
    
    return users.slice(offset, offset + limit);
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private async logActivity(
    user: User,
    action: string,
    resource: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const activity: UserActivity = {
      id: this.generateActivityId(),
      userId: user.profile.id,
      action,
      resource,
      timestamp: new Date(),
      metadata,
    };

    if (!user.activities) {
      user.activities = [];
    }

    user.activities.push(activity);

    // 限制活动记录数量
    if (user.activities.length > 1000) {
      user.activities = user.activities.slice(-1000);
    }
  }

  private async hashPassword(password: string): Promise<string> {
    // 这里应该使用真正的密码哈希库
    return `hashed_${password}`;
  }

  private generateUserId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateAuthId(): string {
    return `auth_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateTransitionId(): string {
    return `transition_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateActivityId(): string {
    return `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
