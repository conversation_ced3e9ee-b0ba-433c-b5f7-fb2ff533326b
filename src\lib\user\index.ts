/**
 * 用户系统主入口
 * 基于易经"天行健，君子以自强不息"的智慧
 * 整合所有用户管理功能，提供统一的API接口
 */

// 导出所有类型定义
export * from './types';

// 导出核心服务
export { UserService } from './user-service';
export { UserStateManager } from './state-manager';
export { AuthManager } from './auth-manager';

// 导出数据持久化相关
export {
  IUserRepository,
  MemoryUserRepository,
  RepositoryFactory,
  MigrationManager,
  type DatabaseConfig,
  type QueryResult,
  type IMigration,
} from './repository';

// 导出配置接口
export type { UserServiceConfig } from './user-service';
export type { AuthConfig, AuthResult } from './auth-manager';

import { UserService, UserServiceConfig } from './user-service';
import { RepositoryFactory, DatabaseConfig } from './repository';
import {
  UserRole,
  UserState,
  AuthMethod,
  Permission,
  UserSystemConfig,
} from './types';

/**
 * 用户系统工厂类
 * 体现"穷则变，变则通，通则久"的设计理念
 */
export class UserSystemFactory {
  /**
   * 创建完整的用户系统实例
   */
  static createUserSystem(config: {
    database: DatabaseConfig;
    auth: {
      jwtSecret: string;
      tokenExpiry?: number;
      refreshTokenExpiry?: number;
      saltRounds?: number;
      maxLoginAttempts?: number;
      lockoutDuration?: number;
    };
    system?: {
      defaultRole?: UserRole;
      defaultState?: UserState;
      enableActivityLogging?: boolean;
      maxUsersPerQuery?: number;
    };
  }): UserSystem {
    const repository = RepositoryFactory.createUserRepository(config.database);
    
    const serviceConfig: UserServiceConfig = {
      auth: {
        jwtSecret: config.auth.jwtSecret,
        tokenExpiry: config.auth.tokenExpiry || 3600, // 1 hour
        refreshTokenExpiry: config.auth.refreshTokenExpiry || 86400 * 7, // 7 days
        saltRounds: config.auth.saltRounds || 12,
        maxLoginAttempts: config.auth.maxLoginAttempts || 5,
        lockoutDuration: config.auth.lockoutDuration || 15, // 15 minutes
      },
      defaultRole: config.system?.defaultRole || UserRole.USER,
      defaultState: config.system?.defaultState || UserState.ACTIVE,
      enableActivityLogging: config.system?.enableActivityLogging ?? true,
      maxUsersPerQuery: config.system?.maxUsersPerQuery || 100,
    };

    const userService = new UserService(serviceConfig);
    
    return new UserSystem(userService, repository);
  }

  /**
   * 创建开发环境用户系统（使用内存存储）
   */
  static createDevelopmentSystem(jwtSecret: string = 'dev-secret'): UserSystem {
    return this.createUserSystem({
      database: { type: 'memory' },
      auth: { jwtSecret },
      system: {
        enableActivityLogging: true,
        maxUsersPerQuery: 50,
      },
    });
  }

  /**
   * 创建生产环境用户系统
   */
  static createProductionSystem(config: {
    databaseUrl: string;
    databaseType: 'mongodb' | 'postgresql' | 'mysql';
    jwtSecret: string;
    authConfig?: Partial<UserServiceConfig['auth']>;
  }): UserSystem {
    return this.createUserSystem({
      database: {
        type: config.databaseType,
        connectionString: config.databaseUrl,
      },
      auth: {
        jwtSecret: config.jwtSecret,
        tokenExpiry: 3600, // 1 hour
        refreshTokenExpiry: 86400 * 30, // 30 days
        saltRounds: 14,
        maxLoginAttempts: 3,
        lockoutDuration: 30, // 30 minutes
        ...config.authConfig,
      },
      system: {
        enableActivityLogging: true,
        maxUsersPerQuery: 100,
      },
    });
  }
}

/**
 * 用户系统主类
 * 提供统一的用户管理接口
 */
export class UserSystem {
  constructor(
    private userService: UserService,
    private repository: any // IUserRepository
  ) {}

  /**
   * 获取用户服务实例
   */
  get users(): UserService {
    return this.userService;
  }

  /**
   * 获取数据仓储实例
   */
  get data(): any {
    return this.repository;
  }

  /**
   * 系统健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    database: 'connected' | 'disconnected';
    userCount: number;
    timestamp: Date;
  }> {
    try {
      const userCount = await this.repository.count();
      
      return {
        status: 'healthy',
        database: 'connected',
        userCount,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        database: 'disconnected',
        userCount: 0,
        timestamp: new Date(),
      };
    }
  }

  /**
   * 系统统计信息
   */
  async getStatistics(): Promise<{
    totalUsers: number;
    activeUsers: number;
    newUsersToday: number;
    usersByState: Record<UserState, number>;
    usersByRole: Record<UserRole, number>;
  }> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const [
      totalUsers,
      activeUsers,
      newUsersToday,
      allUsers,
    ] = await Promise.all([
      this.repository.count(),
      this.repository.count({ state: [UserState.ACTIVE] }),
      this.repository.count({ createdAfter: today }),
      this.repository.query({}, { limit: 10000 }), // 获取所有用户进行统计
    ]);

    // 统计用户状态分布
    const usersByState: Record<UserState, number> = {
      [UserState.INACTIVE]: 0,
      [UserState.ACTIVE]: 0,
      [UserState.SUSPENDED]: 0,
      [UserState.ARCHIVED]: 0,
      [UserState.PENDING]: 0,
    };

    // 统计用户角色分布
    const usersByRole: Record<UserRole, number> = {
      [UserRole.GUEST]: 0,
      [UserRole.USER]: 0,
      [UserRole.MODERATOR]: 0,
      [UserRole.ADMIN]: 0,
      [UserRole.SUPER_ADMIN]: 0,
    };

    for (const user of allUsers.data) {
      usersByState[user.state]++;
      for (const role of user.permissions.roles) {
        usersByRole[role]++;
      }
    }

    return {
      totalUsers,
      activeUsers,
      newUsersToday,
      usersByState,
      usersByRole,
    };
  }

  /**
   * 清理过期数据
   */
  async cleanup(): Promise<{
    expiredTokens: number;
    archivedUsers: number;
    cleanedActivities: number;
  }> {
    // 这里应该实现清理逻辑
    // 1. 清理过期的认证令牌
    // 2. 归档长期不活跃的用户
    // 3. 清理过期的活动记录

    return {
      expiredTokens: 0,
      archivedUsers: 0,
      cleanedActivities: 0,
    };
  }
}

/**
 * 默认用户系统配置
 */
export const DEFAULT_USER_SYSTEM_CONFIG: UserSystemConfig = {
  defaultRole: UserRole.USER,
  defaultState: UserState.ACTIVE,
  stateTransitionRules: [], // 由StateManager自动初始化
  authMethods: [
    AuthMethod.EMAIL_PASSWORD,
    AuthMethod.JWT,
    AuthMethod.API_KEY,
  ],
  passwordPolicy: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
  },
  sessionConfig: {
    maxAge: 3600, // 1 hour
    renewThreshold: 300, // 5 minutes
  },
};

/**
 * 用户系统工具函数
 */
export const UserSystemUtils = {
  /**
   * 验证密码强度
   */
  validatePassword(password: string, policy = DEFAULT_USER_SYSTEM_CONFIG.passwordPolicy!): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (password.length < policy.minLength) {
      errors.push(`Password must be at least ${policy.minLength} characters long`);
    }

    if (policy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (policy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (policy.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (policy.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * 生成安全的随机密码
   */
  generateSecurePassword(length: number = 12): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    return password;
  },

  /**
   * 格式化用户显示名称
   */
  formatDisplayName(user: { displayName?: string; username?: string; email: string }): string {
    return user.displayName || user.username || user.email.split('@')[0];
  },

  /**
   * 检查邮箱格式
   */
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * 检查用户名格式
   */
  isValidUsername(username: string): boolean {
    const usernameRegex = /^[a-zA-Z0-9_-]{3,20}$/;
    return usernameRegex.test(username);
  },
};
