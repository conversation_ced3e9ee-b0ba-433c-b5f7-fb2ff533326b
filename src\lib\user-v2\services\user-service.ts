/**
 * 用户系统v2 - 用户服务
 * 基于道德经"治大国若烹小鲜"原则 - 精细管理，轻柔操作
 * 
 * <AUTHOR>
 * @version 2.0.0
 */

import {
  User,
  UserIdentity,
  UserProfile,
  UserStatus,
  UserRole,
  CreateUserRequest,
  UpdateUserRequest,
  UserFilter,
  QueryOptions,
  QueryResult,
  AuthResult,
  OperationResult,
  AuthMethod,
  UserAuth,
  UserPermissions,
  Permission,
  UserSystemError,
  ErrorFactory,
  UserErrorCode,
  UserId,
  Metadata
} from '../types';

import { IAuthManager } from '../auth';

// ============================================================================
// 用户服务接口
// ============================================================================

/**
 * 用户服务接口
 * 体现"道生一"的统一服务抽象
 */
export interface IUserService {
  // 基础CRUD操作
  createUser(request: CreateUserRequest): Promise<User>;
  getUserById(id: UserId, options?: { includeAuth?: boolean }): Promise<User | null>;
  getUserByEmail(email: string): Promise<User | null>;
  updateUser(id: UserId, updates: UpdateUserRequest): Promise<User>;
  deleteUser(id: UserId, soft?: boolean): Promise<void>;

  // 查询操作
  queryUsers(filter?: UserFilter, options?: QueryOptions): Promise<QueryResult<User>>;
  searchUsers(query: string, options?: QueryOptions): Promise<QueryResult<User>>;
  
  // 状态管理
  changeUserStatus(id: UserId, status: UserStatus, reason?: string): Promise<User>;
  activateUser(id: UserId): Promise<User>;
  suspendUser(id: UserId, reason?: string): Promise<User>;
  
  // 认证相关
  authenticate(identifier: string, credentials: string, method: AuthMethod): Promise<AuthResult>;
  addAuthMethod(userId: UserId, method: AuthMethod, credentials: string): Promise<UserAuth>;
  removeAuthMethod(userId: UserId, method: AuthMethod): Promise<void>;
  
  // 权限管理
  grantRole(userId: UserId, role: UserRole): Promise<void>;
  revokeRole(userId: UserId, role: UserRole): Promise<void>;
  grantPermission(userId: UserId, permission: Permission): Promise<void>;
  revokePermission(userId: UserId, permission: Permission): Promise<void>;
  
  // 统计信息
  getUserStats(): Promise<{
    total: number;
    active: number;
    suspended: number;
    byRole: Record<UserRole, number>;
    byStatus: Record<UserStatus, number>;
  }>;
}

// ============================================================================
// 用户仓储接口
// ============================================================================

/**
 * 用户仓储接口
 * 体现"一生二"的数据分离
 */
export interface IUserRepository {
  // 基础操作
  save(user: User): Promise<User>;
  findById(id: UserId): Promise<User | null>;
  findByEmail(email: string): Promise<User | null>;
  findByApiKey(apiKey: string): Promise<User | null>;
  findByOAuthId(oauthId: string, provider: string): Promise<User | null>;
  delete(id: UserId): Promise<void>;
  
  // 查询操作
  query(filter: UserFilter, options: QueryOptions): Promise<QueryResult<User>>;
  search(query: string, options: QueryOptions): Promise<QueryResult<User>>;
  count(filter?: UserFilter): Promise<number>;
  
  // 批量操作
  saveMany(users: User[]): Promise<User[]>;
  findMany(ids: UserId[]): Promise<User[]>;
  
  // 统计操作
  getStats(): Promise<{
    total: number;
    byStatus: Record<UserStatus, number>;
    byRole: Record<UserRole, number>;
  }>;
}

// ============================================================================
// 用户服务实现
// ============================================================================

/**
 * 用户服务实现
 * 体现"无为而治"原则 - 简洁高效的用户管理
 */
export class UserService implements IUserService {
  constructor(
    private readonly repository: IUserRepository,
    private readonly authManager: IAuthManager,
    private readonly config: {
      defaultRole: UserRole;
      defaultStatus: UserStatus;
      enableActivityLogging: boolean;
      maxUsersPerQuery: number;
    }
  ) {}

  // ============================================================================
  // 基础CRUD操作
  // ============================================================================

  /**
   * 创建用户
   */
  async createUser(request: CreateUserRequest): Promise<User> {
    // 验证输入
    this.validateCreateUserRequest(request);

    // 检查邮箱是否已存在
    const existingUser = await this.repository.findByEmail(request.email);
    if (existingUser) {
      throw new UserSystemError(
        'Email already exists',
        UserErrorCode.USER_ALREADY_EXISTS
      );
    }

    // 生成用户ID
    const userId = this.generateUserId();

    // 创建用户身份
    const identity: UserIdentity = {
      id: userId,
      email: request.email,
      username: request.username,
      externalId: request.metadata?.externalId as string
    };

    // 创建用户档案
    const profile: UserProfile = {
      displayName: request.displayName,
      language: 'en',
      timezone: 'UTC',
      ...request.profile
    };

    // 创建认证信息
    const auth: UserAuth[] = [];
    if (request.password && request.authMethod === AuthMethod.EMAIL) {
      // 这里需要通过认证提供者来生成认证信息
      // 暂时创建一个基础的认证对象
      const emailAuth: UserAuth = {
        method: AuthMethod.EMAIL,
        identifier: request.email,
        credentials: request.password, // 实际应用中需要哈希处理
        isVerified: false,
        metadata: {
          createdAt: new Date().toISOString()
        }
      };
      auth.push(emailAuth);
    }

    // 创建权限信息
    const permissions: UserPermissions = {
      roles: [request.initialRole || this.config.defaultRole],
      permissions: [],
      customPermissions: [],
      restrictions: []
    };

    // 创建用户对象
    const user: User = {
      identity,
      profile,
      status: this.config.defaultStatus,
      createdAt: new Date(),
      updatedAt: new Date(),
      auth,
      permissions,
      metadata: request.metadata || {}
    };

    // 保存用户
    const savedUser = await this.repository.save(user);

    // 记录活动日志
    if (this.config.enableActivityLogging) {
      await this.logActivity(savedUser, 'user_created', {
        authMethod: request.authMethod,
        initialRole: request.initialRole
      });
    }

    return savedUser;
  }

  /**
   * 根据ID获取用户
   */
  async getUserById(id: UserId, options: { includeAuth?: boolean } = {}): Promise<User | null> {
    const user = await this.repository.findById(id);
    
    if (!user) {
      return null;
    }

    // 如果不需要认证信息，则移除敏感数据
    if (!options.includeAuth) {
      return {
        ...user,
        auth: user.auth.map(auth => ({
          ...auth,
          credentials: undefined // 移除敏感凭据
        }))
      };
    }

    return user;
  }

  /**
   * 根据邮箱获取用户
   */
  async getUserByEmail(email: string): Promise<User | null> {
    return this.repository.findByEmail(email);
  }

  /**
   * 更新用户
   */
  async updateUser(id: UserId, updates: UpdateUserRequest): Promise<User> {
    const user = await this.repository.findById(id);
    if (!user) {
      throw ErrorFactory.userNotFound(id);
    }

    // 验证更新请求
    this.validateUpdateUserRequest(updates);

    // 应用更新
    const updatedUser: User = {
      ...user,
      profile: { ...user.profile, ...updates.profile },
      status: updates.status || user.status,
      metadata: { ...user.metadata, ...updates.metadata },
      tags: updates.tags || user.tags,
      updatedAt: new Date()
    };

    // 保存更新
    const savedUser = await this.repository.save(updatedUser);

    // 记录活动日志
    if (this.config.enableActivityLogging) {
      await this.logActivity(savedUser, 'user_updated', { updates });
    }

    return savedUser;
  }

  /**
   * 删除用户
   */
  async deleteUser(id: UserId, soft: boolean = true): Promise<void> {
    const user = await this.repository.findById(id);
    if (!user) {
      throw ErrorFactory.userNotFound(id);
    }

    if (soft) {
      // 软删除 - 更改状态
      await this.changeUserStatus(id, UserStatus.DELETED, 'User deleted');
    } else {
      // 硬删除 - 物理删除
      await this.repository.delete(id);
    }

    // 记录活动日志
    if (this.config.enableActivityLogging) {
      await this.logActivity(user, 'user_deleted', { soft });
    }
  }

  // ============================================================================
  // 查询操作
  // ============================================================================

  /**
   * 查询用户
   */
  async queryUsers(filter: UserFilter = {}, options: QueryOptions = {}): Promise<QueryResult<User>> {
    // 限制查询数量
    const queryOptions = {
      ...options,
      limit: Math.min(options.limit || 20, this.config.maxUsersPerQuery)
    };

    return this.repository.query(filter, queryOptions);
  }

  /**
   * 搜索用户
   */
  async searchUsers(query: string, options: QueryOptions = {}): Promise<QueryResult<User>> {
    const queryOptions = {
      ...options,
      limit: Math.min(options.limit || 20, this.config.maxUsersPerQuery)
    };

    return this.repository.search(query, queryOptions);
  }

  // ============================================================================
  // 状态管理
  // ============================================================================

  /**
   * 更改用户状态
   */
  async changeUserStatus(id: UserId, status: UserStatus, reason?: string): Promise<User> {
    const user = await this.repository.findById(id);
    if (!user) {
      throw ErrorFactory.userNotFound(id);
    }

    // 验证状态转换
    this.validateStatusTransition(user.status, status);

    // 更新状态
    const updatedUser: User = {
      ...user,
      status,
      updatedAt: new Date()
    };

    const savedUser = await this.repository.save(updatedUser);

    // 记录活动日志
    if (this.config.enableActivityLogging) {
      await this.logActivity(savedUser, 'status_changed', {
        fromStatus: user.status,
        toStatus: status,
        reason
      });
    }

    return savedUser;
  }

  /**
   * 激活用户
   */
  async activateUser(id: UserId): Promise<User> {
    return this.changeUserStatus(id, UserStatus.ACTIVE, 'User activated');
  }

  /**
   * 暂停用户
   */
  async suspendUser(id: UserId, reason?: string): Promise<User> {
    return this.changeUserStatus(id, UserStatus.SUSPENDED, reason || 'User suspended');
  }

  // ============================================================================
  // 认证相关
  // ============================================================================

  /**
   * 用户认证
   */
  async authenticate(identifier: string, credentials: string, method: AuthMethod): Promise<AuthResult> {
    const result = await this.authManager.authenticate(identifier, credentials, method);

    // 记录认证活动
    if (this.config.enableActivityLogging && result.user) {
      await this.logActivity(result.user, 'user_authenticated', {
        method,
        success: result.success
      });
    }

    return result;
  }

  /**
   * 添加认证方法
   */
  async addAuthMethod(userId: UserId, method: AuthMethod, credentials: string): Promise<UserAuth> {
    const user = await this.repository.findById(userId);
    if (!user) {
      throw ErrorFactory.userNotFound(userId);
    }

    // 检查是否已存在该认证方法
    const existingAuth = user.auth.find(auth => auth.method === method);
    if (existingAuth) {
      throw new UserSystemError(
        `Authentication method ${method} already exists for user`,
        UserErrorCode.INVALID_INPUT
      );
    }

    // 生成新的认证信息
    // 这里需要通过认证提供者来生成认证信息
    const newAuth: UserAuth = {
      method,
      identifier: method === AuthMethod.EMAIL ? credentials : userId,
      credentials: credentials, // 实际应用中需要适当处理
      isVerified: false,
      metadata: {
        createdAt: new Date().toISOString()
      }
    };
    
    // 更新用户
    const updatedUser: User = {
      ...user,
      auth: [...user.auth, newAuth],
      updatedAt: new Date()
    };

    await this.repository.save(updatedUser);

    // 记录活动日志
    if (this.config.enableActivityLogging) {
      await this.logActivity(updatedUser, 'auth_method_added', { method });
    }

    return newAuth;
  }

  /**
   * 移除认证方法
   */
  async removeAuthMethod(userId: UserId, method: AuthMethod): Promise<void> {
    const user = await this.repository.findById(userId);
    if (!user) {
      throw ErrorFactory.userNotFound(userId);
    }

    // 检查是否存在该认证方法
    const authIndex = user.auth.findIndex(auth => auth.method === method);
    if (authIndex === -1) {
      throw new UserSystemError(
        `Authentication method ${method} not found for user`,
        UserErrorCode.INVALID_INPUT
      );
    }

    // 确保不是最后一个认证方法
    if (user.auth.length === 1) {
      throw new UserSystemError(
        'Cannot remove the last authentication method',
        UserErrorCode.INVALID_INPUT
      );
    }

    // 移除认证方法
    const updatedUser: User = {
      ...user,
      auth: user.auth.filter((_, index) => index !== authIndex),
      updatedAt: new Date()
    };

    await this.repository.save(updatedUser);

    // 记录活动日志
    if (this.config.enableActivityLogging) {
      await this.logActivity(updatedUser, 'auth_method_removed', { method });
    }
  }

  // ============================================================================
  // 私有辅助方法
  // ============================================================================

  /**
   * 生成用户ID
   */
  private generateUserId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 验证创建用户请求
   */
  private validateCreateUserRequest(request: CreateUserRequest): void {
    if (!request.email || !this.isValidEmail(request.email)) {
      throw ErrorFactory.validationFailed('email', request.email, 'Valid email required');
    }

    if (!request.displayName || request.displayName.trim().length === 0) {
      throw ErrorFactory.validationFailed('displayName', request.displayName, 'Display name required');
    }

    if (request.password && request.authMethod === AuthMethod.EMAIL) {
      const passwordValidation = this.validatePassword(request.password);
      if (!passwordValidation.isValid) {
        throw ErrorFactory.validationFailed('password', '***', passwordValidation.message);
      }
    }
  }

  /**
   * 验证更新用户请求
   */
  private validateUpdateUserRequest(updates: UpdateUserRequest): void {
    if (updates.profile?.displayName !== undefined && updates.profile.displayName.trim().length === 0) {
      throw ErrorFactory.validationFailed('displayName', updates.profile.displayName, 'Display name cannot be empty');
    }
  }

  /**
   * 验证状态转换
   */
  private validateStatusTransition(fromStatus: UserStatus, toStatus: UserStatus): void {
    // 定义允许的状态转换
    const allowedTransitions: Record<UserStatus, UserStatus[]> = {
      [UserStatus.ACTIVE]: [UserStatus.INACTIVE, UserStatus.SUSPENDED, UserStatus.DELETED],
      [UserStatus.INACTIVE]: [UserStatus.ACTIVE, UserStatus.SUSPENDED, UserStatus.DELETED],
      [UserStatus.SUSPENDED]: [UserStatus.ACTIVE, UserStatus.DELETED],
      [UserStatus.DELETED]: [] // 删除状态不可逆
    };

    if (!allowedTransitions[fromStatus]?.includes(toStatus)) {
      throw new UserSystemError(
        `Invalid status transition from ${fromStatus} to ${toStatus}`,
        UserErrorCode.INVALID_INPUT
      );
    }
  }

  /**
   * 验证邮箱格式
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 验证密码强度
   */
  private validatePassword(password: string): { isValid: boolean; message: string } {
    if (password.length < 8) {
      return { isValid: false, message: 'Password must be at least 8 characters long' };
    }

    if (!/[A-Z]/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one uppercase letter' };
    }

    if (!/[a-z]/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one lowercase letter' };
    }

    if (!/\d/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one number' };
    }

    return { isValid: true, message: 'Password is valid' };
  }

  // ============================================================================
  // 权限管理
  // ============================================================================

  /**
   * 授予角色
   */
  async grantRole(userId: UserId, role: UserRole): Promise<void> {
    const user = await this.repository.findById(userId);
    if (!user) {
      throw ErrorFactory.userNotFound(userId);
    }

    if (user.permissions.roles.includes(role)) {
      return; // 角色已存在
    }

    const updatedUser: User = {
      ...user,
      permissions: {
        ...user.permissions,
        roles: [...user.permissions.roles, role]
      },
      updatedAt: new Date()
    };

    await this.repository.save(updatedUser);

    if (this.config.enableActivityLogging) {
      await this.logActivity(updatedUser, 'role_granted', { role });
    }
  }

  /**
   * 撤销角色
   */
  async revokeRole(userId: UserId, role: UserRole): Promise<void> {
    const user = await this.repository.findById(userId);
    if (!user) {
      throw ErrorFactory.userNotFound(userId);
    }

    const updatedUser: User = {
      ...user,
      permissions: {
        ...user.permissions,
        roles: user.permissions.roles.filter(r => r !== role)
      },
      updatedAt: new Date()
    };

    await this.repository.save(updatedUser);

    if (this.config.enableActivityLogging) {
      await this.logActivity(updatedUser, 'role_revoked', { role });
    }
  }

  /**
   * 授予权限
   */
  async grantPermission(userId: UserId, permission: Permission): Promise<void> {
    const user = await this.repository.findById(userId);
    if (!user) {
      throw ErrorFactory.userNotFound(userId);
    }

    if (user.permissions.permissions.includes(permission)) {
      return; // 权限已存在
    }

    const updatedUser: User = {
      ...user,
      permissions: {
        ...user.permissions,
        permissions: [...user.permissions.permissions, permission]
      },
      updatedAt: new Date()
    };

    await this.repository.save(updatedUser);

    if (this.config.enableActivityLogging) {
      await this.logActivity(updatedUser, 'permission_granted', { permission });
    }
  }

  /**
   * 撤销权限
   */
  async revokePermission(userId: UserId, permission: Permission): Promise<void> {
    const user = await this.repository.findById(userId);
    if (!user) {
      throw ErrorFactory.userNotFound(userId);
    }

    const updatedUser: User = {
      ...user,
      permissions: {
        ...user.permissions,
        permissions: user.permissions.permissions.filter(p => p !== permission)
      },
      updatedAt: new Date()
    };

    await this.repository.save(updatedUser);

    if (this.config.enableActivityLogging) {
      await this.logActivity(updatedUser, 'permission_revoked', { permission });
    }
  }

  // ============================================================================
  // 统计信息
  // ============================================================================

  /**
   * 获取用户统计信息
   */
  async getUserStats(): Promise<{
    total: number;
    active: number;
    suspended: number;
    byRole: Record<UserRole, number>;
    byStatus: Record<UserStatus, number>;
  }> {
    const stats = await this.repository.getStats();

    return {
      total: stats.total,
      active: stats.byStatus[UserStatus.ACTIVE] || 0,
      suspended: stats.byStatus[UserStatus.SUSPENDED] || 0,
      byRole: stats.byRole,
      byStatus: stats.byStatus
    };
  }

  /**
   * 记录活动日志
   */
  private async logActivity(user: User, action: string, metadata: Metadata): Promise<void> {
    // 这里应该实现活动日志记录逻辑
    // 可以发送到日志系统、数据库或消息队列
    console.log(`[USER_ACTIVITY] ${user.identity.id} - ${action}`, metadata);
  }
}
