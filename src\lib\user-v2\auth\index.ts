/**
 * 用户系统v2 - 认证模块入口
 * 基于道德经"道法自然"原则 - 自然简洁的模块导出
 * 
 * <AUTHOR>
 * @version 2.0.0
 */

// ============================================================================
// 认证提供者导出
// ============================================================================

export {
  // 接口
  IAuthProvider,
  
  // 具体实现
  EmailPasswordProvider,
  JwtProvider,
  ApiKeyProvider,
  OAuthProvider,
  BiometricProvider,
  
  // 工厂
  AuthProviderFactory
} from './auth-provider';

// ============================================================================
// 认证管理器导出
// ============================================================================

export {
  // 接口
  IAuthManager,
  
  // 实现
  AuthManager
} from './auth-manager';

// ============================================================================
// 工具函数导出
// ============================================================================

/**
 * 认证工具类
 * 体现"大道至简"原则 - 简化常用操作
 */
export class AuthUtils {
  /**
   * 生成安全的随机字符串
   */
  static generateSecureRandom(length: number = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  /**
   * 生成API密钥
   */
  static generateApiKey(): string {
    const prefix = 'ak_';
    const timestamp = Date.now().toString(36);
    const random = this.generateSecureRandom(24);
    
    return `${prefix}${timestamp}_${random}`;
  }

  /**
   * 验证邮箱格式
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 验证密码强度
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean;
    score: number;
    feedback: string[];
  } {
    const feedback: string[] = [];
    let score = 0;

    // 长度检查
    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('Password should be at least 8 characters long');
    }

    // 大写字母
    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Password should contain at least one uppercase letter');
    }

    // 小写字母
    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Password should contain at least one lowercase letter');
    }

    // 数字
    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push('Password should contain at least one number');
    }

    // 特殊字符
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Password should contain at least one special character');
    }

    return {
      isValid: score >= 4,
      score,
      feedback
    };
  }

  /**
   * 生成安全密码
   */
  static generateSecurePassword(length: number = 12): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    const allChars = lowercase + uppercase + numbers + symbols;
    let password = '';

    // 确保包含每种类型的字符
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];

    // 填充剩余长度
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // 打乱字符顺序
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  /**
   * 计算密码哈希（简化版，实际应使用bcrypt等）
   */
  static async hashPassword(password: string, saltRounds: number = 12): Promise<string> {
    // 这里应该使用真正的密码哈希库，如bcrypt
    // 这只是一个示例实现
    const salt = this.generateSecureRandom(16);
    const hash = Buffer.from(password + salt).toString('base64');
    return `${saltRounds}$${salt}$${hash}`;
  }

  /**
   * 验证密码（简化版）
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      const [, salt, storedHash] = hash.split('$');
      const computedHash = Buffer.from(password + salt).toString('base64');
      return computedHash === storedHash;
    } catch {
      return false;
    }
  }

  /**
   * 生成JWT载荷
   */
  static createJwtPayload(userId: string, options: {
    expiresIn?: number;
    issuer?: string;
    audience?: string;
    subject?: string;
  } = {}): any {
    const now = Math.floor(Date.now() / 1000);
    
    return {
      userId,
      iat: now,
      exp: now + (options.expiresIn || 3600), // 默认1小时
      iss: options.issuer || 'user-system-v2',
      aud: options.audience || 'user-system-v2',
      sub: options.subject || userId
    };
  }

  /**
   * 创建刷新令牌载荷
   */
  static createRefreshTokenPayload(userId: string, expiresIn: number = 86400 * 7): any {
    const now = Math.floor(Date.now() / 1000);
    
    return {
      userId,
      type: 'refresh',
      iat: now,
      exp: now + expiresIn, // 默认7天
      iss: 'user-system-v2'
    };
  }

  /**
   * 验证令牌是否即将过期
   */
  static isTokenExpiringSoon(payload: any, thresholdMinutes: number = 5): boolean {
    if (!payload.exp) {
      return false;
    }

    const now = Math.floor(Date.now() / 1000);
    const threshold = thresholdMinutes * 60;
    
    return (payload.exp - now) <= threshold;
  }

  /**
   * 提取令牌信息
   */
  static extractTokenInfo(payload: any): {
    userId: string;
    issuedAt: Date;
    expiresAt: Date;
    issuer?: string;
    audience?: string;
    type?: string;
  } {
    return {
      userId: payload.userId || payload.sub,
      issuedAt: new Date(payload.iat * 1000),
      expiresAt: new Date(payload.exp * 1000),
      issuer: payload.iss,
      audience: payload.aud,
      type: payload.type
    };
  }

  /**
   * 生成设备指纹
   */
  static generateDeviceFingerprint(userAgent: string, ipAddress: string): string {
    const data = `${userAgent}|${ipAddress}|${Date.now()}`;
    return Buffer.from(data).toString('base64').slice(0, 32);
  }

  /**
   * 验证设备指纹
   */
  static validateDeviceFingerprint(
    fingerprint: string,
    userAgent: string,
    ipAddress: string
  ): boolean {
    // 简化的设备指纹验证
    // 实际应用中可能需要更复杂的逻辑
    return fingerprint.length === 32;
  }
}

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 认证相关常量
 */
export const AUTH_CONSTANTS = {
  // 默认过期时间（秒）
  DEFAULT_TOKEN_EXPIRY: 3600,           // 1小时
  DEFAULT_REFRESH_TOKEN_EXPIRY: 604800, // 7天
  DEFAULT_API_KEY_EXPIRY: 31536000,     // 1年

  // 安全配置
  DEFAULT_SALT_ROUNDS: 12,
  DEFAULT_MAX_LOGIN_ATTEMPTS: 5,
  DEFAULT_LOCKOUT_DURATION: 15,         // 分钟

  // 令牌类型
  TOKEN_TYPES: {
    ACCESS: 'access',
    REFRESH: 'refresh',
    API_KEY: 'api_key'
  },

  // 认证头
  AUTH_HEADERS: {
    AUTHORIZATION: 'Authorization',
    API_KEY: 'X-API-Key',
    DEVICE_ID: 'X-Device-ID'
  },

  // 认证方案
  AUTH_SCHEMES: {
    BEARER: 'Bearer',
    BASIC: 'Basic',
    API_KEY: 'ApiKey'
  }
} as const;
