/**
 * 用户系统v2 - 类型定义入口
 * 基于道德经"道法自然"原则 - 自然简洁的类型导出
 * 
 * <AUTHOR>
 * @version 2.0.0
 */

// ============================================================================
// 核心类型导出
// ============================================================================

export {
  // 枚举类型
  UserStatus,
  AuthMethod,
  UserRole,
  Permission,
  
  // 核心接口
  UserIdentity,
  UserProfile,
  UserAuth,
  UserPermissions,
  UserContext,
  User,
  
  // 操作接口
  CreateUserRequest,
  UpdateUserRequest,
  UserFilter,
  QueryOptions,
  QueryResult,
  AuthResult,
  OperationResult,
  
  // 配置接口
  AuthConfig,
  SystemConfig,
  DatabaseConfig,
  UserSystemConfig
} from './core';

// ============================================================================
// 错误类型导出
// ============================================================================

export {
  // 错误枚举
  UserErrorCode,
  ErrorSeverity,
  
  // 错误接口
  ErrorInfo,
  
  // 错误类
  UserSystemError,
  ErrorFactory,
  ErrorHandler
} from './errors';

// ============================================================================
// 类型别名 - 简化常用类型
// ============================================================================

/**
 * 用户ID类型
 */
export type UserId = string;

/**
 * 会话ID类型
 */
export type SessionId = string;

/**
 * 令牌类型
 */
export type Token = string;

/**
 * 时间戳类型
 */
export type Timestamp = Date;

/**
 * 元数据类型
 */
export type Metadata = Record<string, unknown>;

/**
 * 标签类型
 */
export type Tags = string[];

// ============================================================================
// 工具类型 - 增强类型安全性
// ============================================================================

/**
 * 部分更新类型 - 用于更新操作
 */
export type PartialUpdate<T> = {
  readonly [P in keyof T]?: T[P];
};

/**
 * 必需字段类型 - 确保关键字段存在
 */
export type RequiredFields<T, K extends keyof T> = T & {
  readonly [P in K]-?: T[P];
};

/**
 * 可选字段类型 - 使指定字段可选
 */
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & {
  readonly [P in K]?: T[P];
};

/**
 * 只读深度类型 - 深度只读
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * 非空类型 - 排除null和undefined
 */
export type NonNullable<T> = T extends null | undefined ? never : T;

// ============================================================================
// 函数类型 - 定义回调和处理器
// ============================================================================

/**
 * 事件处理器类型
 */
export type EventHandler<T = unknown> = (event: T) => void | Promise<void>;

/**
 * 验证器类型
 */
export type Validator<T> = (value: T) => boolean | string;

/**
 * 转换器类型
 */
export type Transformer<T, U> = (input: T) => U;

/**
 * 过滤器类型
 */
export type Filter<T> = (item: T) => boolean;

/**
 * 比较器类型
 */
export type Comparator<T> = (a: T, b: T) => number;

// ============================================================================
// 条件类型 - 高级类型操作
// ============================================================================

/**
 * 提取函数返回类型
 */
export type ExtractReturnType<T> = T extends (...args: any[]) => infer R ? R : never;

/**
 * 提取Promise类型
 */
export type ExtractPromiseType<T> = T extends Promise<infer U> ? U : T;

/**
 * 提取数组元素类型
 */
export type ExtractArrayType<T> = T extends (infer U)[] ? U : never;

/**
 * 键值对类型
 */
export type KeyValuePair<T> = {
  readonly [K in keyof T]: {
    readonly key: K;
    readonly value: T[K];
  };
}[keyof T];

// ============================================================================
// 联合类型 - 常用组合
// ============================================================================

/**
 * 基础数据类型
 */
export type Primitive = string | number | boolean | null | undefined;

/**
 * 序列化值类型
 */
export type SerializableValue = Primitive | SerializableObject | SerializableArray;

/**
 * 序列化对象类型
 */
export type SerializableObject = {
  readonly [key: string]: SerializableValue;
};

/**
 * 序列化数组类型
 */
export type SerializableArray = readonly SerializableValue[];

/**
 * JSON值类型
 */
export type JsonValue = SerializableValue;

// ============================================================================
// 映射类型 - 类型转换
// ============================================================================

/**
 * 字符串键映射
 */
export type StringKeyMap<T> = {
  readonly [key: string]: T;
};

/**
 * 数字键映射
 */
export type NumberKeyMap<T> = {
  readonly [key: number]: T;
};

/**
 * 枚举值映射
 */
export type EnumValueMap<T extends Record<string, string | number>> = {
  readonly [K in keyof T]: T[K];
};

// ============================================================================
// 实用类型 - 常用工具类型
// ============================================================================

/**
 * 空对象类型
 */
export type EmptyObject = Record<string, never>;

/**
 * 未知对象类型
 */
export type UnknownObject = Record<string, unknown>;

/**
 * 任意对象类型
 */
export type AnyObject = Record<string, any>;

/**
 * 构造函数类型
 */
export type Constructor<T = {}> = new (...args: any[]) => T;

/**
 * 抽象构造函数类型
 */
export type AbstractConstructor<T = {}> = abstract new (...args: any[]) => T;

/**
 * 类类型
 */
export type Class<T = {}> = Constructor<T>;

// ============================================================================
// 品牌类型 - 类型安全增强
// ============================================================================

/**
 * 品牌类型基础
 */
declare const __brand: unique symbol;

/**
 * 品牌类型
 */
export type Brand<T, B> = T & { readonly [__brand]: B };

/**
 * 邮箱类型
 */
export type Email = Brand<string, 'Email'>;

/**
 * URL类型
 */
export type Url = Brand<string, 'Url'>;

/**
 * 哈希值类型
 */
export type Hash = Brand<string, 'Hash'>;

/**
 * 加密密钥类型
 */
export type SecretKey = Brand<string, 'SecretKey'>;

// ============================================================================
// 模板字面量类型 - 字符串模式
// ============================================================================

/**
 * 邮箱模式
 */
export type EmailPattern = `${string}@${string}.${string}`;

/**
 * URL模式
 */
export type UrlPattern = `http${'s' | ''}://${string}`;

/**
 * UUID模式
 */
export type UuidPattern = `${string}-${string}-${string}-${string}-${string}`;

/**
 * 版本号模式
 */
export type VersionPattern = `${number}.${number}.${number}`;

// ============================================================================
// 导出声明
// ============================================================================

/**
 * 类型检查工具
 */
export declare namespace TypeGuards {
  function isString(value: unknown): value is string;
  function isNumber(value: unknown): value is number;
  function isBoolean(value: unknown): value is boolean;
  function isObject(value: unknown): value is object;
  function isArray(value: unknown): value is unknown[];
  function isFunction(value: unknown): value is Function;
  function isDate(value: unknown): value is Date;
  function isNull(value: unknown): value is null;
  function isUndefined(value: unknown): value is undefined;
  function isNullish(value: unknown): value is null | undefined;
  function isEmail(value: string): value is Email;
  function isUrl(value: string): value is Url;
  function isUuid(value: string): value is UuidPattern;
}
