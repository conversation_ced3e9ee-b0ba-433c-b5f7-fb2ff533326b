/**
 * 认证和授权管理器
 * 体现易经"适应性"智慧，支持多种认证方式和动态权限调整
 */

import * as crypto from 'crypto';
import {
  User,
  UserAuth,
  UserRole,
  Permission,
  AuthMethod,
  UserPermissions,
  UserSystemError,
  UserErrorCodes,
} from './types';

export interface AuthResult {
  success: boolean;
  user?: User;
  token?: string;
  expiresAt?: Date;
  error?: string;
}

export interface AuthConfig {
  jwtSecret: string;
  tokenExpiry: number; // in seconds
  refreshTokenExpiry: number;
  saltRounds: number;
  maxLoginAttempts: number;
  lockoutDuration: number; // in minutes
}

export class AuthManager {
  private config: AuthConfig;
  private loginAttempts: Map<string, { count: number; lastAttempt: Date }> = new Map();

  constructor(config: AuthConfig) {
    this.config = config;
  }

  /**
   * 用户认证 - 支持多种认证方式
   * 体现"变化适应"的灵活性
   */
  public async authenticate(
    identifier: string,
    credentials: string,
    method: AuthMethod = AuthMethod.EMAIL_PASSWORD
  ): Promise<AuthResult> {
    try {
      // 检查账户锁定状态
      if (this.isAccountLocked(identifier)) {
        return {
          success: false,
          error: 'Account temporarily locked due to too many failed attempts',
        };
      }

      // 根据认证方式执行不同的认证逻辑
      let authResult: AuthResult;
      
      switch (method) {
        case AuthMethod.EMAIL_PASSWORD:
          authResult = await this.authenticateEmailPassword(identifier, credentials);
          break;
        case AuthMethod.API_KEY:
          authResult = await this.authenticateApiKey(identifier, credentials);
          break;
        case AuthMethod.JWT:
          authResult = await this.authenticateJWT(credentials);
          break;
        case AuthMethod.OAUTH_GOOGLE:
        case AuthMethod.OAUTH_GITHUB:
          authResult = await this.authenticateOAuth(identifier, credentials, method);
          break;
        default:
          return {
            success: false,
            error: 'Unsupported authentication method',
          };
      }

      // 处理认证结果
      if (authResult.success) {
        this.clearLoginAttempts(identifier);
        await this.updateLastLogin(authResult.user!);
      } else {
        this.recordFailedAttempt(identifier);
      }

      return authResult;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      };
    }
  }

  /**
   * 邮箱密码认证
   */
  private async authenticateEmailPassword(email: string, password: string): Promise<AuthResult> {
    // 这里应该从数据库查询用户
    // 暂时返回模拟结果
    const user = await this.findUserByEmail(email);
    if (!user) {
      return { success: false, error: 'User not found' };
    }

    const userAuth = user.auth.find(auth => 
      auth.method === AuthMethod.EMAIL_PASSWORD && auth.identifier === email
    );

    if (!userAuth || !userAuth.isVerified) {
      return { success: false, error: 'Invalid credentials' };
    }

    const isValidPassword = await this.verifyPassword(password, userAuth.credentials!);
    if (!isValidPassword) {
      return { success: false, error: 'Invalid credentials' };
    }

    const token = this.generateJWT(user);
    const expiresAt = new Date(Date.now() + this.config.tokenExpiry * 1000);

    return {
      success: true,
      user,
      token,
      expiresAt,
    };
  }

  /**
   * API Key认证
   */
  private async authenticateApiKey(userId: string, apiKey: string): Promise<AuthResult> {
    const user = await this.findUserById(userId);
    if (!user) {
      return { success: false, error: 'User not found' };
    }

    const userAuth = user.auth.find(auth => 
      auth.method === AuthMethod.API_KEY && auth.credentials === apiKey
    );

    if (!userAuth || !userAuth.isVerified) {
      return { success: false, error: 'Invalid API key' };
    }

    // 检查API Key是否过期
    if (userAuth.expiresAt && userAuth.expiresAt < new Date()) {
      return { success: false, error: 'API key expired' };
    }

    return {
      success: true,
      user,
    };
  }

  /**
   * JWT认证
   */
  private async authenticateJWT(token: string): Promise<AuthResult> {
    try {
      const payload = this.verifyJWT(token);
      const user = await this.findUserById(payload.userId);
      
      if (!user) {
        return { success: false, error: 'User not found' };
      }

      return {
        success: true,
        user,
        token,
        expiresAt: new Date(payload.exp * 1000),
      };
    } catch (error) {
      return { success: false, error: 'Invalid token' };
    }
  }

  /**
   * OAuth认证
   */
  private async authenticateOAuth(
    oauthId: string,
    accessToken: string,
    method: AuthMethod
  ): Promise<AuthResult> {
    // 验证OAuth token
    const oauthUser = await this.verifyOAuthToken(accessToken, method);
    if (!oauthUser || oauthUser.id !== oauthId) {
      return { success: false, error: 'Invalid OAuth token' };
    }

    // 查找或创建用户
    let user = await this.findUserByOAuth(oauthId, method);
    if (!user) {
      user = await this.createUserFromOAuth(oauthUser, method);
    }

    const token = this.generateJWT(user);
    const expiresAt = new Date(Date.now() + this.config.tokenExpiry * 1000);

    return {
      success: true,
      user,
      token,
      expiresAt,
    };
  }

  /**
   * 权限检查
   * 体现"平衡性"原则
   */
  public hasPermission(user: User, permission: Permission): boolean {
    // 检查直接权限
    if (user.permissions.permissions.includes(permission)) {
      return true;
    }

    // 检查角色权限
    return this.roleHasPermission(user.permissions.roles, permission);
  }

  /**
   * 角色权限检查
   */
  public hasRole(user: User, role: UserRole): boolean {
    return user.permissions.roles.includes(role);
  }

  /**
   * 检查多个权限
   */
  public hasAllPermissions(user: User, permissions: Permission[]): boolean {
    return permissions.every(permission => this.hasPermission(user, permission));
  }

  /**
   * 检查任一权限
   */
  public hasAnyPermission(user: User, permissions: Permission[]): boolean {
    return permissions.some(permission => this.hasPermission(user, permission));
  }

  /**
   * 动态添加权限
   */
  public async grantPermission(
    user: User,
    permission: Permission,
    expiresAt?: Date
  ): Promise<void> {
    if (!user.permissions.permissions.includes(permission)) {
      user.permissions.permissions.push(permission);
    }
    
    if (expiresAt) {
      user.permissions.expiresAt = expiresAt;
    }
    
    user.updatedAt = new Date();
  }

  /**
   * 撤销权限
   */
  public async revokePermission(user: User, permission: Permission): Promise<void> {
    const index = user.permissions.permissions.indexOf(permission);
    if (index > -1) {
      user.permissions.permissions.splice(index, 1);
      user.updatedAt = new Date();
    }
  }

  /**
   * 添加角色
   */
  public async grantRole(user: User, role: UserRole): Promise<void> {
    if (!user.permissions.roles.includes(role)) {
      user.permissions.roles.push(role);
      user.updatedAt = new Date();
    }
  }

  /**
   * 撤销角色
   */
  public async revokeRole(user: User, role: UserRole): Promise<void> {
    const index = user.permissions.roles.indexOf(role);
    if (index > -1) {
      user.permissions.roles.splice(index, 1);
      user.updatedAt = new Date();
    }
  }

  // 私有辅助方法

  private roleHasPermission(roles: UserRole[], permission: Permission): boolean {
    const rolePermissions: Record<UserRole, Permission[]> = {
      [UserRole.GUEST]: [Permission.READ],
      [UserRole.USER]: [Permission.READ, Permission.WRITE],
      [UserRole.MODERATOR]: [Permission.READ, Permission.WRITE, Permission.MODERATE],
      [UserRole.ADMIN]: [Permission.READ, Permission.WRITE, Permission.MODERATE, Permission.DELETE],
      [UserRole.SUPER_ADMIN]: [Permission.READ, Permission.WRITE, Permission.MODERATE, Permission.DELETE, Permission.ADMIN],
    };

    return roles.some(role => rolePermissions[role]?.includes(permission));
  }

  private async hashPassword(password: string): Promise<string> {
    const salt = crypto.randomBytes(16).toString('hex');
    const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
    return `${salt}:${hash}`;
  }

  private async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    const [salt, hash] = hashedPassword.split(':');
    const verifyHash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
    return hash === verifyHash;
  }

  private generateJWT(user: User): string {
    const payload = {
      userId: user.profile.id,
      email: user.profile.email,
      roles: user.permissions.roles,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + this.config.tokenExpiry,
    };

    // 这里应该使用真正的JWT库
    return Buffer.from(JSON.stringify(payload)).toString('base64');
  }

  private verifyJWT(token: string): any {
    // 这里应该使用真正的JWT库验证
    try {
      return JSON.parse(Buffer.from(token, 'base64').toString());
    } catch {
      throw new Error('Invalid token');
    }
  }

  private isAccountLocked(identifier: string): boolean {
    const attempts = this.loginAttempts.get(identifier);
    if (!attempts) return false;

    const lockoutTime = this.config.lockoutDuration * 60 * 1000;
    const isLocked = attempts.count >= this.config.maxLoginAttempts &&
                    (Date.now() - attempts.lastAttempt.getTime()) < lockoutTime;

    return isLocked;
  }

  private recordFailedAttempt(identifier: string): void {
    const attempts = this.loginAttempts.get(identifier) || { count: 0, lastAttempt: new Date() };
    attempts.count++;
    attempts.lastAttempt = new Date();
    this.loginAttempts.set(identifier, attempts);
  }

  private clearLoginAttempts(identifier: string): void {
    this.loginAttempts.delete(identifier);
  }

  // 这些方法应该连接到实际的数据库
  private async findUserByEmail(email: string): Promise<User | null> {
    // 模拟数据库查询
    return null;
  }

  private async findUserById(id: string): Promise<User | null> {
    // 模拟数据库查询
    return null;
  }

  private async findUserByOAuth(oauthId: string, method: AuthMethod): Promise<User | null> {
    // 模拟数据库查询
    return null;
  }

  private async createUserFromOAuth(oauthUser: any, method: AuthMethod): Promise<User> {
    // 模拟从OAuth信息创建用户
    throw new Error('Not implemented');
  }

  private async verifyOAuthToken(token: string, method: AuthMethod): Promise<any> {
    // 模拟OAuth token验证
    return null;
  }

  private async updateLastLogin(user: User): Promise<void> {
    user.lastLoginAt = new Date();
    user.lastActiveAt = new Date();
  }
}
