/**
 * 用户系统v2 - 基础仓储
 * 基于道德经"变则通"原则 - 灵活适应不同存储方式
 * 
 * <AUTHOR>
 * @version 2.0.0
 */

import {
  User,
  UserFilter,
  QueryOptions,
  QueryResult,
  UserStatus,
  UserRole,
  UserId,
  UserSystemError,
  ErrorFactory,
  UserErrorCode
} from '../types';

// ============================================================================
// 基础仓储接口
// ============================================================================

/**
 * 基础仓储接口
 * 体现"道生一"的统一抽象
 */
export interface IBaseRepository<T, ID> {
  // 基础CRUD
  save(entity: T): Promise<T>;
  findById(id: ID): Promise<T | null>;
  findMany(ids: ID[]): Promise<T[]>;
  delete(id: ID): Promise<void>;
  
  // 查询操作
  query(filter: any, options: QueryOptions): Promise<QueryResult<T>>;
  count(filter?: any): Promise<number>;
  
  // 批量操作
  saveMany(entities: T[]): Promise<T[]>;
  deleteMany(ids: ID[]): Promise<void>;
  
  // 事务支持
  transaction<R>(fn: (repo: this) => Promise<R>): Promise<R>;
}

/**
 * 用户仓储接口
 * 继承基础仓储，添加用户特定方法
 */
export interface IUserRepository extends IBaseRepository<User, UserId> {
  // 用户特定查找方法
  findByEmail(email: string): Promise<User | null>;
  findByUsername(username: string): Promise<User | null>;
  findByApiKey(apiKey: string): Promise<User | null>;
  findByOAuthId(oauthId: string, provider: string): Promise<User | null>;
  
  // 用户特定查询
  search(query: string, options: QueryOptions): Promise<QueryResult<User>>;
  findByStatus(status: UserStatus, options?: QueryOptions): Promise<QueryResult<User>>;
  findByRole(role: UserRole, options?: QueryOptions): Promise<QueryResult<User>>;
  
  // 统计方法
  getStats(): Promise<{
    total: number;
    byStatus: Record<UserStatus, number>;
    byRole: Record<UserRole, number>;
  }>;
}

// ============================================================================
// 内存仓储实现
// ============================================================================

/**
 * 内存用户仓储
 * 体现"朴素"原则 - 简单直接的内存存储
 */
export class MemoryUserRepository implements IUserRepository {
  private users: Map<UserId, User> = new Map();
  private emailIndex: Map<string, UserId> = new Map();
  private usernameIndex: Map<string, UserId> = new Map();
  private apiKeyIndex: Map<string, UserId> = new Map();
  private oauthIndex: Map<string, UserId> = new Map();

  // ============================================================================
  // 基础CRUD操作
  // ============================================================================

  async save(user: User): Promise<User> {
    const userId = user.identity.id;
    
    // 更新主存储
    this.users.set(userId, { ...user });
    
    // 更新索引
    this.updateIndexes(user);
    
    return { ...user };
  }

  async findById(id: UserId): Promise<User | null> {
    const user = this.users.get(id);
    return user ? { ...user } : null;
  }

  async findMany(ids: UserId[]): Promise<User[]> {
    const users: User[] = [];
    
    for (const id of ids) {
      const user = await this.findById(id);
      if (user) {
        users.push(user);
      }
    }
    
    return users;
  }

  async delete(id: UserId): Promise<void> {
    const user = this.users.get(id);
    if (!user) {
      throw ErrorFactory.userNotFound(id);
    }
    
    // 删除主记录
    this.users.delete(id);
    
    // 清理索引
    this.removeFromIndexes(user);
  }

  // ============================================================================
  // 用户特定查找方法
  // ============================================================================

  async findByEmail(email: string): Promise<User | null> {
    const userId = this.emailIndex.get(email.toLowerCase());
    return userId ? this.findById(userId) : null;
  }

  async findByUsername(username: string): Promise<User | null> {
    const userId = this.usernameIndex.get(username.toLowerCase());
    return userId ? this.findById(userId) : null;
  }

  async findByApiKey(apiKey: string): Promise<User | null> {
    const userId = this.apiKeyIndex.get(apiKey);
    return userId ? this.findById(userId) : null;
  }

  async findByOAuthId(oauthId: string, provider: string): Promise<User | null> {
    const key = `${provider}:${oauthId}`;
    const userId = this.oauthIndex.get(key);
    return userId ? this.findById(userId) : null;
  }

  // ============================================================================
  // 查询操作
  // ============================================================================

  async query(filter: UserFilter, options: QueryOptions): Promise<QueryResult<User>> {
    let users = Array.from(this.users.values());
    
    // 应用过滤器
    users = this.applyFilter(users, filter);
    
    // 应用排序
    users = this.applySort(users, options);
    
    // 计算分页
    const total = users.length;
    const offset = options.offset || 0;
    const limit = options.limit || 20;
    
    const paginatedUsers = users.slice(offset, offset + limit);
    
    return {
      data: paginatedUsers.map(user => ({ ...user })),
      total,
      hasMore: offset + limit < total,
      nextOffset: offset + limit < total ? offset + limit : undefined
    };
  }

  async search(query: string, options: QueryOptions): Promise<QueryResult<User>> {
    const searchTerm = query.toLowerCase();
    let users = Array.from(this.users.values());
    
    // 搜索匹配
    users = users.filter(user => 
      user.identity.email.toLowerCase().includes(searchTerm) ||
      user.identity.username?.toLowerCase().includes(searchTerm) ||
      user.profile.displayName.toLowerCase().includes(searchTerm)
    );
    
    // 应用排序
    users = this.applySort(users, options);
    
    // 计算分页
    const total = users.length;
    const offset = options.offset || 0;
    const limit = options.limit || 20;
    
    const paginatedUsers = users.slice(offset, offset + limit);
    
    return {
      data: paginatedUsers.map(user => ({ ...user })),
      total,
      hasMore: offset + limit < total,
      nextOffset: offset + limit < total ? offset + limit : undefined
    };
  }

  async findByStatus(status: UserStatus, options: QueryOptions = {}): Promise<QueryResult<User>> {
    return this.query({ status: [status] }, options);
  }

  async findByRole(role: UserRole, options: QueryOptions = {}): Promise<QueryResult<User>> {
    return this.query({ roles: [role] }, options);
  }

  async count(filter?: UserFilter): Promise<number> {
    if (!filter) {
      return this.users.size;
    }
    
    const users = Array.from(this.users.values());
    return this.applyFilter(users, filter).length;
  }

  // ============================================================================
  // 批量操作
  // ============================================================================

  async saveMany(users: User[]): Promise<User[]> {
    const savedUsers: User[] = [];
    
    for (const user of users) {
      const savedUser = await this.save(user);
      savedUsers.push(savedUser);
    }
    
    return savedUsers;
  }

  async deleteMany(ids: UserId[]): Promise<void> {
    for (const id of ids) {
      await this.delete(id);
    }
  }

  // ============================================================================
  // 事务支持
  // ============================================================================

  async transaction<R>(fn: (repo: this) => Promise<R>): Promise<R> {
    // 内存仓储的简单事务实现
    // 创建当前状态的快照
    const snapshot = {
      users: new Map(this.users),
      emailIndex: new Map(this.emailIndex),
      usernameIndex: new Map(this.usernameIndex),
      apiKeyIndex: new Map(this.apiKeyIndex),
      oauthIndex: new Map(this.oauthIndex)
    };
    
    try {
      return await fn(this);
    } catch (error) {
      // 回滚到快照状态
      this.users = snapshot.users;
      this.emailIndex = snapshot.emailIndex;
      this.usernameIndex = snapshot.usernameIndex;
      this.apiKeyIndex = snapshot.apiKeyIndex;
      this.oauthIndex = snapshot.oauthIndex;
      
      throw error;
    }
  }

  // ============================================================================
  // 统计方法
  // ============================================================================

  async getStats(): Promise<{
    total: number;
    byStatus: Record<UserStatus, number>;
    byRole: Record<UserRole, number>;
  }> {
    const users = Array.from(this.users.values());
    const total = users.length;
    
    // 按状态统计
    const byStatus: Record<UserStatus, number> = {
      [UserStatus.ACTIVE]: 0,
      [UserStatus.INACTIVE]: 0,
      [UserStatus.SUSPENDED]: 0,
      [UserStatus.DELETED]: 0
    };
    
    // 按角色统计
    const byRole: Record<UserRole, number> = {
      [UserRole.GUEST]: 0,
      [UserRole.USER]: 0,
      [UserRole.PREMIUM]: 0,
      [UserRole.MODERATOR]: 0,
      [UserRole.ADMIN]: 0,
      [UserRole.SUPER_ADMIN]: 0
    };
    
    for (const user of users) {
      // 统计状态
      byStatus[user.status]++;
      
      // 统计角色
      for (const role of user.permissions.roles) {
        byRole[role]++;
      }
    }
    
    return { total, byStatus, byRole };
  }

  // ============================================================================
  // 私有辅助方法
  // ============================================================================

  private updateIndexes(user: User): void {
    const userId = user.identity.id;
    
    // 更新邮箱索引
    this.emailIndex.set(user.identity.email.toLowerCase(), userId);
    
    // 更新用户名索引
    if (user.identity.username) {
      this.usernameIndex.set(user.identity.username.toLowerCase(), userId);
    }
    
    // 更新API密钥索引
    for (const auth of user.auth) {
      if (auth.method === 'api_key' && auth.credentials) {
        this.apiKeyIndex.set(auth.credentials, userId);
      }
      
      // 更新OAuth索引
      if (auth.method === 'oauth' && auth.metadata?.provider) {
        const key = `${auth.metadata.provider}:${auth.identifier}`;
        this.oauthIndex.set(key, userId);
      }
    }
  }

  private removeFromIndexes(user: User): void {
    // 清理邮箱索引
    this.emailIndex.delete(user.identity.email.toLowerCase());
    
    // 清理用户名索引
    if (user.identity.username) {
      this.usernameIndex.delete(user.identity.username.toLowerCase());
    }
    
    // 清理认证相关索引
    for (const auth of user.auth) {
      if (auth.method === 'api_key' && auth.credentials) {
        this.apiKeyIndex.delete(auth.credentials);
      }
      
      if (auth.method === 'oauth' && auth.metadata?.provider) {
        const key = `${auth.metadata.provider}:${auth.identifier}`;
        this.oauthIndex.delete(key);
      }
    }
  }

  private applyFilter(users: User[], filter: UserFilter): User[] {
    return users.filter(user => {
      // 状态过滤
      if (filter.status && !filter.status.includes(user.status)) {
        return false;
      }
      
      // 角色过滤
      if (filter.roles && !filter.roles.some(role => user.permissions.roles.includes(role))) {
        return false;
      }
      
      // 创建时间过滤
      if (filter.createdAfter && user.createdAt < filter.createdAfter) {
        return false;
      }
      
      if (filter.createdBefore && user.createdAt > filter.createdBefore) {
        return false;
      }
      
      // 最后登录时间过滤
      if (filter.lastLoginAfter && (!user.lastLoginAt || user.lastLoginAt < filter.lastLoginAfter)) {
        return false;
      }
      
      // 标签过滤
      if (filter.tags && (!user.tags || !filter.tags.some(tag => user.tags!.includes(tag)))) {
        return false;
      }
      
      // 搜索过滤
      if (filter.search) {
        const searchTerm = filter.search.toLowerCase();
        const searchableText = [
          user.identity.email,
          user.identity.username,
          user.profile.displayName
        ].filter(Boolean).join(' ').toLowerCase();
        
        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }
      
      return true;
    });
  }

  private applySort(users: User[], options: QueryOptions): User[] {
    if (!options.sortBy) {
      return users;
    }
    
    const sortBy = options.sortBy;
    const sortOrder = options.sortOrder || 'asc';
    
    return users.sort((a, b) => {
      let aValue: any;
      let bValue: any;
      
      switch (sortBy) {
        case 'createdAt':
          aValue = a.createdAt.getTime();
          bValue = b.createdAt.getTime();
          break;
        case 'updatedAt':
          aValue = a.updatedAt.getTime();
          bValue = b.updatedAt.getTime();
          break;
        case 'lastLoginAt':
          aValue = a.lastLoginAt?.getTime() || 0;
          bValue = b.lastLoginAt?.getTime() || 0;
          break;
        case 'email':
          aValue = a.identity.email;
          bValue = b.identity.email;
          break;
        case 'displayName':
          aValue = a.profile.displayName;
          bValue = b.profile.displayName;
          break;
        default:
          return 0;
      }
      
      if (aValue < bValue) {
        return sortOrder === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortOrder === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }
}
