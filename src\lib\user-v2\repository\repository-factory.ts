/**
 * 用户系统v2 - 仓储工厂
 * 基于道德经"因材施教"原则 - 根据需要创建合适的仓储
 * 
 * <AUTHOR>
 * @version 2.0.0
 */

import {
  DatabaseConfig,
  UserSystemError,
  UserErrorCode
} from '../types';

import {
  IUserRepository,
  MemoryUserRepository
} from './base-repository';

// ============================================================================
// 数据库适配器接口
// ============================================================================

/**
 * 数据库连接接口
 */
export interface IDatabaseConnection {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  isConnected(): boolean;
  getClient(): any;
}

/**
 * MongoDB适配器接口
 */
export interface IMongoAdapter extends IDatabaseConnection {
  getCollection(name: string): any;
  createIndex(collection: string, index: any): Promise<void>;
}

/**
 * SQL适配器接口
 */
export interface ISqlAdapter extends IDatabaseConnection {
  query(sql: string, params?: any[]): Promise<any>;
  transaction<T>(fn: (client: any) => Promise<T>): Promise<T>;
}

// ============================================================================
// MongoDB仓储实现
// ============================================================================

/**
 * MongoDB用户仓储
 * 体现"水善利万物而不争"原则 - 灵活适应文档存储
 */
export class MongoUserRepository implements IUserRepository {
  constructor(
    private readonly adapter: IMongoAdapter,
    private readonly collectionName: string = 'users'
  ) {}

  async save(user: any): Promise<any> {
    const collection = this.adapter.getCollection(this.collectionName);
    
    const result = await collection.replaceOne(
      { 'identity.id': user.identity.id },
      user,
      { upsert: true }
    );
    
    return user;
  }

  async findById(id: string): Promise<any | null> {
    const collection = this.adapter.getCollection(this.collectionName);
    return collection.findOne({ 'identity.id': id });
  }

  async findByEmail(email: string): Promise<any | null> {
    const collection = this.adapter.getCollection(this.collectionName);
    return collection.findOne({ 'identity.email': email.toLowerCase() });
  }

  async findByUsername(username: string): Promise<any | null> {
    const collection = this.adapter.getCollection(this.collectionName);
    return collection.findOne({ 'identity.username': username.toLowerCase() });
  }

  async findByApiKey(apiKey: string): Promise<any | null> {
    const collection = this.adapter.getCollection(this.collectionName);
    return collection.findOne({ 'auth.credentials': apiKey });
  }

  async findByOAuthId(oauthId: string, provider: string): Promise<any | null> {
    const collection = this.adapter.getCollection(this.collectionName);
    return collection.findOne({
      'auth.identifier': oauthId,
      'auth.metadata.provider': provider
    });
  }

  async findMany(ids: string[]): Promise<any[]> {
    const collection = this.adapter.getCollection(this.collectionName);
    const cursor = collection.find({ 'identity.id': { $in: ids } });
    return cursor.toArray();
  }

  async delete(id: string): Promise<void> {
    const collection = this.adapter.getCollection(this.collectionName);
    await collection.deleteOne({ 'identity.id': id });
  }

  async query(filter: any, options: any): Promise<any> {
    const collection = this.adapter.getCollection(this.collectionName);
    
    // 构建MongoDB查询
    const mongoFilter = this.buildMongoFilter(filter);
    
    // 构建排序
    const sort = this.buildMongoSort(options);
    
    // 执行查询
    const total = await collection.countDocuments(mongoFilter);
    const cursor = collection
      .find(mongoFilter)
      .sort(sort)
      .skip(options.offset || 0)
      .limit(options.limit || 20);
    
    const data = await cursor.toArray();
    
    return {
      data,
      total,
      hasMore: (options.offset || 0) + (options.limit || 20) < total,
      nextOffset: (options.offset || 0) + (options.limit || 20) < total 
        ? (options.offset || 0) + (options.limit || 20) 
        : undefined
    };
  }

  async search(query: string, options: any): Promise<any> {
    const collection = this.adapter.getCollection(this.collectionName);
    
    const searchFilter = {
      $or: [
        { 'identity.email': { $regex: query, $options: 'i' } },
        { 'identity.username': { $regex: query, $options: 'i' } },
        { 'profile.displayName': { $regex: query, $options: 'i' } }
      ]
    };
    
    const total = await collection.countDocuments(searchFilter);
    const cursor = collection
      .find(searchFilter)
      .sort(this.buildMongoSort(options))
      .skip(options.offset || 0)
      .limit(options.limit || 20);
    
    const data = await cursor.toArray();
    
    return {
      data,
      total,
      hasMore: (options.offset || 0) + (options.limit || 20) < total,
      nextOffset: (options.offset || 0) + (options.limit || 20) < total 
        ? (options.offset || 0) + (options.limit || 20) 
        : undefined
    };
  }

  async findByStatus(status: string, options: any = {}): Promise<any> {
    return this.query({ status: [status] }, options);
  }

  async findByRole(role: string, options: any = {}): Promise<any> {
    return this.query({ roles: [role] }, options);
  }

  async count(filter?: any): Promise<number> {
    const collection = this.adapter.getCollection(this.collectionName);
    const mongoFilter = filter ? this.buildMongoFilter(filter) : {};
    return collection.countDocuments(mongoFilter);
  }

  async saveMany(users: any[]): Promise<any[]> {
    const collection = this.adapter.getCollection(this.collectionName);
    
    const operations = users.map(user => ({
      replaceOne: {
        filter: { 'identity.id': user.identity.id },
        replacement: user,
        upsert: true
      }
    }));
    
    await collection.bulkWrite(operations);
    return users;
  }

  async deleteMany(ids: string[]): Promise<void> {
    const collection = this.adapter.getCollection(this.collectionName);
    await collection.deleteMany({ 'identity.id': { $in: ids } });
  }

  async transaction<R>(fn: (repo: this) => Promise<R>): Promise<R> {
    // MongoDB事务实现
    const session = this.adapter.getClient().startSession();
    
    try {
      return await session.withTransaction(async () => {
        return fn(this);
      });
    } finally {
      await session.endSession();
    }
  }

  async getStats(): Promise<any> {
    const collection = this.adapter.getCollection(this.collectionName);
    
    const pipeline = [
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          byStatus: {
            $push: {
              status: '$status',
              count: 1
            }
          },
          byRole: {
            $push: {
              roles: '$permissions.roles',
              count: 1
            }
          }
        }
      }
    ];
    
    const result = await collection.aggregate(pipeline).toArray();
    
    if (result.length === 0) {
      return {
        total: 0,
        byStatus: {},
        byRole: {}
      };
    }
    
    // 处理聚合结果
    const stats = result[0];
    
    // 统计状态分布
    const byStatus: any = {};
    for (const item of stats.byStatus) {
      byStatus[item.status] = (byStatus[item.status] || 0) + 1;
    }
    
    // 统计角色分布
    const byRole: any = {};
    for (const item of stats.byRole) {
      for (const role of item.roles) {
        byRole[role] = (byRole[role] || 0) + 1;
      }
    }
    
    return {
      total: stats.total,
      byStatus,
      byRole
    };
  }

  private buildMongoFilter(filter: any): any {
    const mongoFilter: any = {};
    
    if (filter.status) {
      mongoFilter.status = { $in: filter.status };
    }
    
    if (filter.roles) {
      mongoFilter['permissions.roles'] = { $in: filter.roles };
    }
    
    if (filter.createdAfter) {
      mongoFilter.createdAt = { ...mongoFilter.createdAt, $gte: filter.createdAfter };
    }
    
    if (filter.createdBefore) {
      mongoFilter.createdAt = { ...mongoFilter.createdAt, $lte: filter.createdBefore };
    }
    
    if (filter.lastLoginAfter) {
      mongoFilter.lastLoginAt = { $gte: filter.lastLoginAfter };
    }
    
    if (filter.tags) {
      mongoFilter.tags = { $in: filter.tags };
    }
    
    if (filter.search) {
      mongoFilter.$or = [
        { 'identity.email': { $regex: filter.search, $options: 'i' } },
        { 'identity.username': { $regex: filter.search, $options: 'i' } },
        { 'profile.displayName': { $regex: filter.search, $options: 'i' } }
      ];
    }
    
    return mongoFilter;
  }

  private buildMongoSort(options: any): any {
    if (!options.sortBy) {
      return { createdAt: -1 };
    }
    
    const sortOrder = options.sortOrder === 'asc' ? 1 : -1;
    
    switch (options.sortBy) {
      case 'email':
        return { 'identity.email': sortOrder };
      case 'displayName':
        return { 'profile.displayName': sortOrder };
      case 'createdAt':
        return { createdAt: sortOrder };
      case 'updatedAt':
        return { updatedAt: sortOrder };
      case 'lastLoginAt':
        return { lastLoginAt: sortOrder };
      default:
        return { createdAt: -1 };
    }
  }
}

// ============================================================================
// 仓储工厂
// ============================================================================

/**
 * 仓储工厂
 * 体现"道法自然"原则 - 自然选择合适的存储方式
 */
export class RepositoryFactory {
  /**
   * 创建用户仓储
   */
  static createUserRepository(config: DatabaseConfig): IUserRepository {
    switch (config.type) {
      case 'memory':
        return new MemoryUserRepository();
        
      case 'mongodb':
        if (!config.connectionString) {
          throw new UserSystemError(
            'MongoDB connection string is required',
            UserErrorCode.CONFIG_ERROR
          );
        }
        
        // 这里应该创建MongoDB适配器
        // const adapter = new MongoAdapter(config.connectionString, config.options);
        // return new MongoUserRepository(adapter);
        
        // 暂时返回内存仓储作为占位符
        console.warn('MongoDB repository not implemented, using memory repository');
        return new MemoryUserRepository();
        
      case 'postgresql':
      case 'mysql':
        if (!config.connectionString) {
          throw new UserSystemError(
            `${config.type} connection string is required`,
            UserErrorCode.CONFIG_ERROR
          );
        }
        
        // 这里应该创建SQL适配器
        // const adapter = new SqlAdapter(config.type, config.connectionString, config.options);
        // return new SqlUserRepository(adapter);
        
        // 暂时返回内存仓储作为占位符
        console.warn(`${config.type} repository not implemented, using memory repository`);
        return new MemoryUserRepository();
        
      default:
        throw new UserSystemError(
          `Unsupported database type: ${config.type}`,
          UserErrorCode.CONFIG_ERROR
        );
    }
  }

  /**
   * 验证数据库配置
   */
  static validateDatabaseConfig(config: DatabaseConfig): void {
    if (!config.type) {
      throw new UserSystemError(
        'Database type is required',
        UserErrorCode.MISSING_CONFIG
      );
    }

    if (config.type !== 'memory' && !config.connectionString) {
      throw new UserSystemError(
        `Connection string is required for ${config.type}`,
        UserErrorCode.MISSING_CONFIG
      );
    }
  }

  /**
   * 获取支持的数据库类型
   */
  static getSupportedDatabaseTypes(): string[] {
    return ['memory', 'mongodb', 'postgresql', 'mysql'];
  }
}
