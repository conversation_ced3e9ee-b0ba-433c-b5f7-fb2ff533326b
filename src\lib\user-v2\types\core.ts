/**
 * 用户系统v2 - 核心类型定义
 * 基于道德经"大道至简"原则设计
 * 
 * <AUTHOR>
 * @version 2.0.0
 */

// ============================================================================
// 基础枚举 - 体现"道生一"的统一性
// ============================================================================

/**
 * 用户状态枚举 - 简化状态管理
 */
export enum UserStatus {
  ACTIVE = 'active',           // 活跃
  INACTIVE = 'inactive',       // 非活跃
  SUSPENDED = 'suspended',     // 暂停
  DELETED = 'deleted'          // 已删除
}

/**
 * 认证方式枚举
 */
export enum AuthMethod {
  EMAIL = 'email',             // 邮箱密码
  PHONE = 'phone',             // 手机验证
  OAUTH = 'oauth',             // 第三方登录
  API_KEY = 'api_key',         // API密钥
  JWT = 'jwt',                 // JWT令牌
  BIOMETRIC = 'biometric'      // 生物识别
}

/**
 * 用户角色枚举 - 层次化权限
 */
export enum UserRole {
  GUEST = 'guest',             // 访客
  USER = 'user',               // 普通用户
  PREMIUM = 'premium',         // 高级用户
  MODERATOR = 'moderator',     // 版主
  ADMIN = 'admin',             // 管理员
  SUPER_ADMIN = 'super_admin'  // 超级管理员
}

/**
 * 权限枚举 - 基础权限集合
 */
export enum Permission {
  READ = 'read',               // 读取
  WRITE = 'write',             // 写入
  DELETE = 'delete',           // 删除
  MODERATE = 'moderate',       // 审核
  ADMIN = 'admin',             // 管理
  SYSTEM = 'system'            // 系统级
}

// ============================================================================
// 核心接口 - 体现"一生二"的分离原则
// ============================================================================

/**
 * 用户身份标识 - 统一身份管理
 */
export interface UserIdentity {
  readonly id: string;         // 唯一标识
  readonly email: string;      // 邮箱地址
  readonly username?: string;  // 用户名
  readonly phone?: string;     // 手机号
  readonly externalId?: string; // 外部系统ID
}

/**
 * 用户档案信息 - 个人资料
 */
export interface UserProfile {
  readonly displayName: string;    // 显示名称
  readonly avatar?: string;        // 头像URL
  readonly bio?: string;           // 个人简介
  readonly location?: string;      // 地理位置
  readonly website?: string;       // 个人网站
  readonly language: string;       // 首选语言
  readonly timezone: string;       // 时区
  readonly dateOfBirth?: Date;     // 出生日期
}

/**
 * 用户认证信息 - 认证凭据
 */
export interface UserAuth {
  readonly method: AuthMethod;     // 认证方式
  readonly identifier: string;    // 认证标识
  readonly isVerified: boolean;   // 是否已验证
  readonly lastUsed?: Date;       // 最后使用时间
  readonly expiresAt?: Date;      // 过期时间
  readonly metadata?: Record<string, unknown>; // 扩展元数据
}

/**
 * 用户权限信息 - 权限管理
 */
export interface UserPermissions {
  readonly roles: UserRole[];              // 用户角色
  readonly permissions: Permission[];      // 直接权限
  readonly customPermissions?: string[];   // 自定义权限
  readonly restrictions?: string[];        // 权限限制
  readonly expiresAt?: Date;              // 权限过期时间
}

/**
 * 用户上下文 - 运行时状态
 */
export interface UserContext {
  readonly sessionId?: string;     // 会话ID
  readonly deviceId?: string;      // 设备ID
  readonly ipAddress?: string;     // IP地址
  readonly userAgent?: string;     // 用户代理
  readonly location?: string;      // 当前位置
  readonly lastActivity: Date;     // 最后活动时间
}

// ============================================================================
// 核心实体 - 体现"二生三"的层次结构
// ============================================================================

/**
 * 用户实体 - 核心用户对象
 * 遵循"无为而治"原则，保持简洁性
 */
export interface User {
  // 身份层 - 不可变标识
  readonly identity: UserIdentity;
  
  // 档案层 - 可变信息
  readonly profile: UserProfile;
  
  // 状态层 - 系统状态
  readonly status: UserStatus;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly lastLoginAt?: Date;
  
  // 认证层 - 认证信息
  readonly auth: UserAuth[];
  
  // 权限层 - 授权信息
  readonly permissions: UserPermissions;
  
  // 上下文层 - 运行时信息
  readonly context?: UserContext;
  
  // 扩展层 - 灵活扩展
  readonly metadata?: Record<string, unknown>;
  readonly tags?: string[];
}

// ============================================================================
// 操作接口 - 体现"三生万物"的扩展性
// ============================================================================

/**
 * 用户创建请求
 */
export interface CreateUserRequest {
  readonly email: string;
  readonly password?: string;
  readonly username?: string;
  readonly displayName: string;
  readonly authMethod: AuthMethod;
  readonly initialRole?: UserRole;
  readonly profile?: Partial<UserProfile>;
  readonly metadata?: Record<string, unknown>;
}

/**
 * 用户更新请求
 */
export interface UpdateUserRequest {
  readonly profile?: Partial<UserProfile>;
  readonly status?: UserStatus;
  readonly metadata?: Record<string, unknown>;
  readonly tags?: string[];
}

/**
 * 用户查询过滤器
 */
export interface UserFilter {
  readonly status?: UserStatus[];
  readonly roles?: UserRole[];
  readonly createdAfter?: Date;
  readonly createdBefore?: Date;
  readonly lastLoginAfter?: Date;
  readonly tags?: string[];
  readonly search?: string;
}

/**
 * 查询选项
 */
export interface QueryOptions {
  readonly limit?: number;
  readonly offset?: number;
  readonly sortBy?: string;
  readonly sortOrder?: 'asc' | 'desc';
  readonly includeAuth?: boolean;
  readonly includeContext?: boolean;
}

/**
 * 查询结果
 */
export interface QueryResult<T> {
  readonly data: T[];
  readonly total: number;
  readonly hasMore: boolean;
  readonly nextOffset?: number;
}

/**
 * 认证结果
 */
export interface AuthResult {
  readonly success: boolean;
  readonly user?: User;
  readonly token?: string;
  readonly refreshToken?: string;
  readonly expiresAt?: Date;
  readonly error?: string;
}

/**
 * 操作结果
 */
export interface OperationResult<T = void> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly code?: string;
}

// ============================================================================
// 配置接口 - 系统配置
// ============================================================================

/**
 * 认证配置
 */
export interface AuthConfig {
  readonly jwtSecret: string;
  readonly tokenExpiry: number;        // 秒
  readonly refreshTokenExpiry: number; // 秒
  readonly maxLoginAttempts: number;
  readonly lockoutDuration: number;    // 分钟
  readonly passwordPolicy: {
    readonly minLength: number;
    readonly requireUppercase: boolean;
    readonly requireLowercase: boolean;
    readonly requireNumbers: boolean;
    readonly requireSpecialChars: boolean;
  };
}

/**
 * 系统配置
 */
export interface SystemConfig {
  readonly defaultRole: UserRole;
  readonly defaultStatus: UserStatus;
  readonly enableActivityLogging: boolean;
  readonly maxUsersPerQuery: number;
  readonly sessionTimeout: number;     // 分钟
  readonly enableCache: boolean;
  readonly cacheExpiry: number;        // 秒
}

/**
 * 数据库配置
 */
export interface DatabaseConfig {
  readonly type: 'memory' | 'mongodb' | 'postgresql' | 'mysql';
  readonly connectionString?: string;
  readonly options?: Record<string, unknown>;
}

/**
 * 用户系统配置
 */
export interface UserSystemConfig {
  readonly auth: AuthConfig;
  readonly system: SystemConfig;
  readonly database: DatabaseConfig;
}
